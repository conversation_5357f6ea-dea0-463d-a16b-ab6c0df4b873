package com.fastclip.biz.controller;

import com.fastclip.common.model.dto.*;
import com.fastclip.common.model.request.*;
import com.fastclip.common.model.response.GetVideoClipsRes;
import com.fastclip.common.model.response.PagebleRes;
import com.fastclip.common.model.template.RequestTemplate;
import com.fastclip.service.project.ProjectService;
import com.fastclip.service.subtitles.SubtitlesService;
import com.fastclip.service.video.VideoClipService;
import com.fastclip.service.video.VideoMaterialClipService;
import com.fastclip.service.video.VideoMaterialService;
import com.fastclip.service.video.VideoSubtitlesFetchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("video")
public class VideoController {

    private RequestTemplate requestTemplate = new RequestTemplate();

    @Autowired
    ProjectService projectService;

    @Autowired
    SubtitlesService subtitlesService;

    @Autowired
    VideoClipService videoClipService;

    @Autowired
    VideoSubtitlesFetchService videoSubtitlesFetchService;

    @Autowired
    VideoMaterialService videoMaterialService;

    @Autowired
    VideoMaterialClipService videoMaterialClipService;

    @PostMapping("play")
    public void streamVideo(HttpServletResponse response){

    }

    @PostMapping("fetchSubtitles")
    public void getSubtitles(@RequestBody VideoMaterialDTO videoMaterialDTO){
        videoSubtitlesFetchService.fetchSubtitles(videoMaterialDTO.getId());
    }

    @PostMapping("refetchSubtitles")
    public void refetchSubtitles(@RequestBody VideoMaterialDTO videoMaterialDTO){
        videoSubtitlesFetchService.refetchSubtitles(videoMaterialDTO.getId());
    }


    @PostMapping("createVideo")
    public RequestTemplate.Response<Boolean> createVideo(@RequestBody CreateVideoReq req){
        return new RequestTemplate().doRequest(() -> videoMaterialService.createVideoMaterial(req));
    }

    @PostMapping("deleteVideo")
    public RequestTemplate.Response<Boolean> deleteVideo(@RequestBody VideoMaterialDTO videoMaterialDTO){
        return new RequestTemplate().doRequest(() -> videoMaterialService.deleteVideoMaterial(videoMaterialDTO));
    }

    @PostMapping("flagStartScene")
    public RequestTemplate.Response<Boolean> flagStartScene(@RequestBody VideoMaterialDTO videoMaterialDTO){
        return new RequestTemplate().doRequest(() -> videoMaterialService.flagVideoMaterialScene(videoMaterialDTO));
    }


    @PostMapping("searchSubtitles")
    public RequestTemplate.Response<List<SubtitlesDTO>> searchSubtitles(@RequestBody SearchSubtitlesReq req){
        return new RequestTemplate().doRequest(() -> subtitlesService.searchSubtitles(req));
    }

    @PostMapping("mergeVideoClips")
    public RequestTemplate.Response<Boolean> mergeVideoClip(@RequestBody MergeVideoClipReq req){
        return new RequestTemplate().doRequest(() -> videoClipService.mergeVideoClips(req));
    }

    @PostMapping("editSubtitles")
    public RequestTemplate.Response<Boolean> editSubtitles(@RequestBody SubtitlesDTO req){
        return new RequestTemplate().doRequest(() -> subtitlesService.updateSubtitlesContent(req));
    }

    @PostMapping("splitVideoClip")
    public RequestTemplate.Response<Boolean> splitideoClip(@RequestBody DeleteVideoClipReq req){
        return new RequestTemplate().doRequest(() -> videoClipService.splitVideoClip(req));
    }


    @PostMapping("getVideoMaterialClips")
    public RequestTemplate.Response<List<VideoMaterialClipDTO>> getVideoMaterialClipList(@RequestBody VideoMaterialClipReq req) {
        return requestTemplate.doRequest(() -> videoMaterialClipService.getVideoMaterialClips(req));
    }

    @PostMapping("getVideoMaterials")
    public RequestTemplate.Response<PagebleRes<VideoMaterialDTO>> getVideoMaterialList(@RequestBody VideoMaterialReq req) {
        return requestTemplate.doRequest(() -> videoMaterialService.getVideoMaterial(req));
    }

    @PostMapping("updateVideoMaterials")
    public RequestTemplate.Response<Boolean> updateVideoMaterialList(@RequestBody UpdateVideoMaterialsReq req) {
        return requestTemplate.doRequest(() -> videoMaterialService.updateVideoMaterials(req));
    }

    @PostMapping("addSubtitlesToProject")
    public RequestTemplate.Response<Boolean> addSubtitlesToProject(@RequestBody AddSubtitlesReq req) {
        return requestTemplate.doRequest(() -> projectService.addSubtitlesClipsToProject(req));
    }

    @PostMapping("removeSubtitlesFromProject")
    public RequestTemplate.Response<Boolean> removeSubtitlesToProject(@RequestBody RemoveSubtitlesReq req) {
        return requestTemplate.doRequest(() -> subtitlesService.removeSubtitlesClipsFromProject(req));
    }

    @PostMapping("searchSubtitlesCuts")
    public RequestTemplate.Response<List<SubtitlesCutDTO>> searchSubtitlesCuts(@RequestBody SearchSubtitlesCutReq req) {
        return requestTemplate.doRequest(() -> subtitlesService.getSubtitlesCut(req));
    }

    @PostMapping("getVideoClips")
    public RequestTemplate.Response<GetVideoClipsRes> getVideoClips(@RequestBody GetVideoClipsReq req) {
        return requestTemplate.doRequest(() -> videoClipService.getVideoClips(req));
    }

    @PostMapping("getWorksIdsOfSubtitlesClip")
    public RequestTemplate.Response<List<Long>> getWorksIdsOfSubtitlesClip(@RequestBody RemoveSubtitlesReq req) {
        return requestTemplate.doRequest(() -> subtitlesService.getWorksIdOfSubtitlesClip(req));
    }

    @PostMapping("updateVideoClips")
    public RequestTemplate.Response<Boolean> updateVideoClips(@RequestBody UpdateVideoClipsReq req) {
        return requestTemplate.doRequest(() -> videoClipService.updateVedioClips(req.getVideoClipDTOs()));
    }

    @GetMapping("getVideoClipTagList")
    public RequestTemplate.Response<List<VideoClipTagDTO>> getVideoClipTagList() {

        return requestTemplate.doRequest(() -> videoClipService.getVideoClipTagList());
    }

    @PostMapping("tagVideoClip")
    public RequestTemplate.Response<Boolean> tagVideoClip(@RequestBody TagVideoClipReq req) {

        return requestTemplate.doRequest(() -> videoClipService.tagVideoClip(req));
    }

    @PostMapping("getVideoMaterialFiles")
    public RequestTemplate.Response<List<VideoMaterialFileDTO>> getVideoClips(@RequestBody GetVideoMaterialsReq req) {
        return requestTemplate.doRequest(() -> videoMaterialService.getVideoMaterialFileDTOs(req.getSellerId()));
    }

    @PostMapping("uploadVideo")
    public RequestTemplate.Response<String> uploadItems(@RequestParam("file") MultipartFile file){
        return new RequestTemplate().doRequest(() -> videoMaterialService.uploadMaterial(file));
    }

    /**
     * Save product markings for a video
     * @param req Save product markings request
     * @return Save result
     */
    @PostMapping("saveProductMarkings")
    public RequestTemplate.Response<SaveProductMarkingsResultDTO> saveProductMarkings(@RequestBody SaveProductMarkingsReq req) {
        return requestTemplate.doRequest(() -> videoMaterialClipService.saveProductMarkings(req));
    }

    /**
     * Get product markings for a video
     * @param req Get product markings request
     * @return List of product markings
     */
    @PostMapping("getProductMarkings")
    public RequestTemplate.Response<List<VideoMaterialClipDTO>> getProductMarkings(@RequestBody GetProductMarkingsReq req) {
        return requestTemplate.doRequest(() -> videoMaterialClipService.getProductMarkings(req));
    }
}
