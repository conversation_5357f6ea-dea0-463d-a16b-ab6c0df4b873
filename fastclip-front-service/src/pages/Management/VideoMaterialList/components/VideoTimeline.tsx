import React from 'react';
import SegmentProgressBar from './SegmentProgressBar';
import ProductLegend from './ProductLegend';
import { type Segment } from './types';
import styles from './ProductMarkingModal.less';

interface TimelineSegmentData {
  startTime: number;
  endTime: number;
  status: 'pending' | 'processing' | 'completed' | 'error' | 'saved';
  productType?: number;
  confidence?: number;
  itemId?: string;
  itemName?: string;
  // Manual selection support
  isManuallySelected?: boolean;
  originalItemId?: string;
  originalItemName?: string;
  originalConfidence?: number;
  allRecognitionResults?: Array<{
    itemId: string;
    itemName: string;
    confidence: number;
  }>;
  // Save status
  isSaved?: boolean;
  savedId?: number;
}

interface VideoTimelineProps {
  segments: TimelineSegmentData[];
  isProcessing: boolean;
  onSegmentClick: (segment: TimelineSegmentData) => void;
  onManualSelect?: (segmentIndex: number, selectedItem: { itemId: string; itemName: string; confidence: number }) => void;
}

const VideoTimeline: React.FC<VideoTimelineProps> = ({
  segments,
  isProcessing,
  onSegmentClick,
  onManualSelect
}) => {
  const formatTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const convertedSegments: Segment[] = segments.map(segment => ({
    start: segment.startTime,
    end: segment.endTime,
    status: segment.status,
    confidence: segment.confidence,
    itemId: segment.itemId,
    itemName: segment.itemName,
    isManuallySelected: segment.isManuallySelected,
    originalItemId: segment.originalItemId,
    originalItemName: segment.originalItemName,
    originalConfidence: segment.originalConfidence,
    allRecognitionResults: segment.allRecognitionResults,
  }));

  const totalDuration = segments.length > 0 ? segments[segments.length - 1].endTime : 0;

  const getStatusCounts = () => {
    const counts = {
      pending: 0,
      processing: 0,
      completed: 0,
      error: 0,
      saved: 0
    };

    segments.forEach(segment => {
      if (segment.status && counts.hasOwnProperty(segment.status)) {
        counts[segment.status as keyof typeof counts]++;
      }
    });

    return counts;
  };

  const statusCounts = getStatusCounts();

  const handleSegmentClick = (idx: number) => {
    const originalSegment = segments[idx];
    if (originalSegment) {
      onSegmentClick(originalSegment);
    }
  };

  return (
    <div className={styles.timelineContainer}>
      {segments.length === 0 ? (
        <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
          暂无视频数据
        </div>
      ) : (
        <>
          <SegmentProgressBar
            duration={totalDuration}
            segments={convertedSegments}
            currentTime={0}
            onSegmentClick={handleSegmentClick}
            onManualSelect={onManualSelect}
          />

          <ProductLegend segments={convertedSegments} />

          <div className={styles.timelineInfo}>
            <div>
              <span>总时长: {formatTime(totalDuration)}</span>
              <span style={{ marginLeft: 16 }}>总片段: {segments.length}</span>
            </div>
            <div>
              {statusCounts.pending > 0 && (
                <span style={{ marginRight: 12, color: '#666' }}>
                  待处理: {statusCounts.pending}
                </span>
              )}
              {statusCounts.processing > 0 && (
                <span style={{ marginRight: 12, color: '#1890ff' }}>
                  处理中: {statusCounts.processing}
                </span>
              )}
              {statusCounts.completed > 0 && (
                <span style={{ marginRight: 12, color: '#52c41a' }}>
                  已完成: {statusCounts.completed}
                </span>
              )}
              {statusCounts.error > 0 && (
                <span style={{ marginRight: 12, color: '#ff4d4f' }}>
                  错误: {statusCounts.error}
                </span>
              )}
              {statusCounts.saved > 0 && (
                <span style={{ marginRight: 12, color: '#722ed1' }}>
                  已保存: {statusCounts.saved}
                </span>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default VideoTimeline;
