# 视频播放器功能测试指南

## 功能概述

本次为商品识别界面添加了基于HLS的视频播放器功能，包括：

### 新增功能
1. ✅ **HLS视频播放器** - 基于Video.js的专业视频播放器
2. ✅ **片段标记显示** - 在视频进度条上显示商品识别片段
3. ✅ **片段跳转控件** - 可点击的片段列表，支持快速跳转
4. ✅ **时间轴同步** - 视频播放器与现有时间轴功能协同工作
5. ✅ **响应式布局** - 适配不同屏幕尺寸的界面布局

### 技术实现
- **前端**: Video.js + videojs-markers + React + TypeScript
- **后端**: HLS视频流配置 (application.yml)
- **样式**: 自定义CSS样式，与Ant Design主题一致

## 测试准备

### 环境要求
- 前端服务正常运行 (端口3000)
- 后端服务正常运行 (端口8078)
- 数据库连接正常
- WebSocket连接正常
- HLS视频文件准备就绪

### 测试数据
- 准备一个有效的视频记录
- 确保视频有对应的HLS文件 (.m3u8)
- 准备一些商品数据用于识别

## 详细测试步骤

### 1. 界面布局测试

#### 1.1 打开商品识别界面
**测试步骤：**
1. 打开视频素材管理页面
2. 选择一个视频，点击"商品标记"
3. 等待商品识别界面加载完成

**预期结果：**
- ✅ 界面正常显示，包含视频信息卡片
- ✅ 新增视频播放器区域 (左侧，占16列)
- ✅ 新增片段控件区域 (右侧，占8列)
- ✅ 原有时间轴正常显示在下方
- ✅ WebSocket连接状态显示为"已连接"

#### 1.2 响应式布局验证
**测试步骤：**
1. 调整浏览器窗口大小
2. 测试不同分辨率下的显示效果

**预期结果：**
- ✅ 视频播放器自适应容器大小
- ✅ 片段控件滚动条正常工作
- ✅ 移动端适配正常

### 2. 视频播放器功能测试

#### 2.1 HLS视频加载
**测试步骤：**
1. 确认视频播放器显示正常
2. 检查HLS视频源是否正确加载
3. 点击播放按钮

**预期结果：**
- ✅ 视频播放器正常初始化
- ✅ HLS视频流正常加载
- ✅ 播放控件响应正常
- ✅ 进度条显示正确

#### 2.2 播放控制功能
**测试步骤：**
1. 测试播放/暂停功能
2. 测试音量控制
3. 测试全屏功能
4. 测试播放速度调节

**预期结果：**
- ✅ 所有播放控件正常工作
- ✅ 快捷键支持正常
- ✅ 全屏模式正常进入/退出

### 3. 片段标记功能测试

#### 3.1 商品识别后的标记显示
**测试步骤：**
1. 点击"开始识别"按钮
2. 等待商品识别完成
3. 观察视频播放器进度条上的标记

**预期结果：**
- ✅ 识别完成的片段在进度条上显示橙色标记
- ✅ 手动选择的片段显示紫色标记
- ✅ 标记位置与时间轴片段对应
- ✅ 鼠标悬浮显示片段信息

#### 3.2 标记点击跳转
**测试步骤：**
1. 点击进度条上的标记点
2. 验证视频是否跳转到对应时间

**预期结果：**
- ✅ 点击标记后视频跳转到正确时间
- ✅ 跳转后自动开始播放
- ✅ 时间显示准确

### 4. 片段控件功能测试

#### 4.1 快速跳转按钮
**测试步骤：**
1. 观察右侧片段控件区域
2. 点击"快速跳转"区域的按钮
3. 验证跳转功能

**预期结果：**
- ✅ 显示所有已识别片段的按钮
- ✅ 按钮显示时间和状态信息
- ✅ 点击按钮后视频跳转到对应时间
- ✅ 按钮样式根据状态变化

#### 4.2 详细信息卡片
**测试步骤：**
1. 观察"详细信息"区域的卡片列表
2. 点击任意卡片
3. 验证跳转和信息显示

**预期结果：**
- ✅ 卡片显示完整的片段信息
- ✅ 包含时间、商品名称、置信度等
- ✅ 点击卡片后视频跳转正确
- ✅ 卡片样式与状态对应

### 5. 时间轴同步测试

#### 5.1 播放器与时间轴同步
**测试步骤：**
1. 在视频播放器中播放视频
2. 观察时间轴是否有相应反馈
3. 在时间轴上点击片段
4. 观察视频播放器是否同步

**预期结果：**
- ✅ 视频播放时间与时间轴状态同步
- ✅ 点击时间轴片段时视频跳转对应
- ✅ 双向同步工作正常

### 6. 错误处理测试

#### 6.1 视频加载失败
**测试步骤：**
1. 使用无效的视频ID
2. 观察错误处理

**预期结果：**
- ✅ 显示友好的错误提示
- ✅ 不影响其他功能正常使用

#### 6.2 网络连接问题
**测试步骤：**
1. 模拟网络中断
2. 观察播放器行为

**预期结果：**
- ✅ 显示网络错误提示
- ✅ 重连后恢复正常

## 性能测试

### 1. 加载性能
- ✅ 视频播放器初始化时间 < 2秒
- ✅ HLS视频首帧显示时间 < 3秒
- ✅ 片段标记渲染时间 < 1秒

### 2. 内存使用
- ✅ 播放器正常释放资源
- ✅ 组件卸载时清理事件监听
- ✅ 长时间使用无内存泄漏

## 兼容性测试

### 浏览器支持
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### 移动端支持
- ✅ iOS Safari
- ✅ Android Chrome
- ✅ 触摸操作正常

## 已知问题和限制

### 当前限制
1. **HLS文件依赖**: 需要预先准备HLS格式的视频文件
2. **实时转码**: 暂未实现实时视频转HLS功能
3. **多清晰度**: 暂不支持自适应码率切换

### 后续改进方向
1. 集成FFmpeg实现实时HLS转码
2. 添加多清晰度支持
3. 支持更多视频格式
4. 添加视频缩略图预览
5. 支持视频片段导出功能

## 部署注意事项

### 后端配置
确保 `application.yml` 中的HLS配置正确：
```yaml
video:
  hls:
    basePath: ${BASE_DATA_PATH:/data/app}/douyin/hls/
    # urlPrefix: ${HLS_URL_PREFIX:http://localhost:8078/api/fastclip/video/hls/}
    segmentDuration: 4
    playlistType: vod
```

### 前端依赖
确保安装了必要的npm包：
```bash
npm install video.js videojs-markers @videojs-player/react
```

### 静态资源
确保HLS视频文件可通过HTTP访问，路径格式：
```
/api/fastclip/video/hls/{videoId}/playlist.m3u8
```

## 测试完成标准

- [ ] 所有界面元素正常显示
- [ ] 视频播放功能完全正常
- [ ] 片段标记和跳转功能正常
- [ ] 时间轴同步功能正常
- [ ] 错误处理机制有效
- [ ] 性能指标达标
- [ ] 兼容性测试通过
- [ ] 用户体验良好

## 联系信息

如遇到问题，请联系开发团队或查看相关文档：
- 技术文档: `/tmp/` 目录下的相关文档
- 代码位置: `fastclip-front-service/src/pages/Management/VideoMaterialList/components/`
