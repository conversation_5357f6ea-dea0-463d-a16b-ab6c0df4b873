import React, { useEffect, useRef, useState } from 'react';
import videojs, { VideoJsPlayer } from 'video.js';
import 'video.js/dist/video-js.css';
import 'videojs-markers';
import { MarkersPluginOptions } from 'videojs-markers';
import { Card, message } from 'antd';
import { PlayCircleOutlined } from '@ant-design/icons';

interface Marker {
  time: number;
  text: string;
  color?: string;
}

interface VideoPlayerProps {
  src: string;           // HLS m3u8 URL
  markers: Marker[];     // 片段标记数组
  onReady?: (player: VideoJsPlayer) => void;
  onTimeUpdate?: (currentTime: number) => void;
  onMarkerClick?: (marker: Marker) => void;
  className?: string;
  style?: React.CSSProperties;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  markers,
  onReady,
  onTimeUpdate,
  onMarkerClick,
  className,
  style
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<VideoJsPlayer>();
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!playerRef.current && videoRef.current && src) {
      try {
        // Initialize Video.js player
        playerRef.current = videojs(videoRef.current, {
          controls: true,
          preload: 'auto',
          fluid: true,
          responsive: true,
          playbackRates: [0.5, 1, 1.25, 1.5, 2],
          sources: [{
            src,
            type: 'application/x-mpegURL' // HLS MIME type
          }],
          html5: {
            hls: {
              enableLowInitialPlaylist: true,
              smoothQualityChange: true,
              overrideNative: true
            }
          }
        });

        // Setup event listeners
        playerRef.current.ready(() => {
          console.log('Video.js player is ready');
          setIsReady(true);
          setError(null);
          onReady?.(playerRef.current!);
        });

        playerRef.current.on('timeupdate', () => {
          if (playerRef.current) {
            onTimeUpdate?.(playerRef.current.currentTime());
          }
        });

        playerRef.current.on('error', (e) => {
          console.error('Video.js player error:', e);
          setError('视频加载失败，请检查视频源');
          message.error('视频加载失败');
        });

        playerRef.current.on('loadstart', () => {
          console.log('Video loading started');
          setError(null);
        });

      } catch (err) {
        console.error('Failed to initialize video player:', err);
        setError('视频播放器初始化失败');
      }
    }

    // Cleanup on unmount
    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = undefined;
        setIsReady(false);
      }
    };
  }, [src]);

  // Update markers when markers prop changes
  useEffect(() => {
    if (playerRef.current && isReady && markers.length > 0) {
      try {
        const markerOptions: MarkersPluginOptions = {
          markers: markers.map(marker => ({
            time: marker.time,
            text: marker.text,
            overlayText: marker.text,
            class: 'video-marker'
          })),
          onMarkerReached: (marker) => {
            console.log('Marker reached:', marker.text);
          },
          onMarkerClick: (marker) => {
            console.log('Marker clicked:', marker.text);
            const originalMarker = markers.find(m => m.time === marker.time);
            if (originalMarker && onMarkerClick) {
              onMarkerClick(originalMarker);
            }
          },
          markerStyle: {
            'background-color': '#fa8c16',
            'border-radius': '2px',
            'height': '8px'
          },
          markerTip: {
            display: true,
            text: (marker) => marker.text
          }
        };

        // @ts-ignore - videojs-markers plugin augmentation
        if (playerRef.current.markers) {
          playerRef.current.markers(markerOptions);
        }
      } catch (err) {
        console.error('Failed to setup markers:', err);
      }
    }
  }, [markers, isReady, onMarkerClick]);

  const handleContainerClick = () => {
    if (playerRef.current && isReady) {
      if (playerRef.current.paused()) {
        playerRef.current.play();
      } else {
        playerRef.current.pause();
      }
    }
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <PlayCircleOutlined />
          视频播放器
        </div>
      }
      size="small"
      className={className}
      style={style}
    >
      <div
        className={`video-player-container ${className || ''}`}
        style={{ position: 'relative', width: '100%', ...style }}
        onClick={handleContainerClick}
      >
        {error ? (
          <div 
            style={{ 
              textAlign: 'center', 
              padding: '40px 0', 
              color: '#ff4d4f',
              backgroundColor: '#fff2f0',
              border: '1px dashed #ffccc7',
              borderRadius: '6px'
            }}
          >
            {error}
          </div>
        ) : (
          <div data-vjs-player style={{ width: '100%' }}>
            <video
              ref={videoRef}
              className="video-js vjs-big-play-centered"
              style={{ width: '100%', height: 'auto' }}
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default VideoPlayer;
