package com.fastclip.common.model.request;

import lombok.Data;

import java.util.List;

/**
 * Save product markings request
 */
@Data
public class SaveProductMarkingsReq {
    
    /**
     * Video ID
     */
    private Long videoId;
    
    /**
     * Seller ID
     */
    private Long sellerId;
    
    /**
     * Product markings list
     */
    private List<ProductMarkingItem> markings;
    
    @Data
    public static class ProductMarkingItem {
        /**
         * Item ID
         */
        private Long itemId;
        
        /**
         * Start timestamp in milliseconds
         */
        private Integer startTs;
        
        /**
         * End timestamp in milliseconds
         */
        private Integer endTs;
        
        /**
         * Duration in milliseconds
         */
        private Integer duration;
    }
}
