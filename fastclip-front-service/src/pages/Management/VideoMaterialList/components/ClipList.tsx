import React from 'react';
import { <PERSON><PERSON>, Card, Tag, Tooltip, Space, Empty } from 'antd';
// import { VideoJsPlayer } from 'video.js';
import { 
  PlayCircleOutlined, 
  ClockCircleOutlined, 
  TagOutlined,
  ShoppingOutlined 
} from '@ant-design/icons';

interface ClipItem {
  time: number;
  text: string;
  itemName?: string;
  confidence?: number;
  status?: 'pending' | 'processing' | 'completed' | 'error' | 'saved';
  isManuallySelected?: boolean;
}

interface ClipListProps {
  player?: any; // VideoJsPlayer type
  clips: ClipItem[];
  onClipClick?: (clip: ClipItem) => void;
  title?: string;
  className?: string;
  style?: React.CSSProperties;
}

const ClipList: React.FC<ClipListProps> = ({ 
  player, 
  clips, 
  onClipClick,
  title = "视频片段",
  className,
  style 
}) => {
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleClipClick = (clip: ClipItem) => {
    // Jump to specific time in video player
    if (player && typeof clip.time === 'number') {
      try {
        player.currentTime(clip.time);
        if (player.paused()) {
          player.play();
        }
        console.log(`Jumped to time: ${clip.time}s`);
      } catch (error) {
        console.error('Failed to jump to time:', error);
      }
    }
    
    // Call external click handler
    onClipClick?.(clip);
  };

  const getStatusColor = (status?: string): string => {
    switch (status) {
      case 'completed': return '#52c41a';
      case 'processing': return '#1890ff';
      case 'saved': return '#722ed1';
      case 'error': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  const getStatusText = (clip: ClipItem): string => {
    if (clip.isManuallySelected) {
      return '手动选择';
    }
    if (clip.status === 'saved') {
      return '已保存';
    }
    if (clip.confidence !== undefined) {
      return `${Math.round(clip.confidence * 100)}%`;
    }
    return clip.status || '待处理';
  };

  const renderClipButton = (clip: ClipItem, index: number) => {
    const isActive = clip.status === 'completed' || clip.status === 'saved';
    
    return (
      <Tooltip
        key={index}
        title={
          <div>
            <div>时间: {formatTime(clip.time)}</div>
            {clip.itemName && <div>商品: {clip.itemName}</div>}
            {clip.confidence !== undefined && (
              <div>置信度: {Math.round(clip.confidence * 100)}%</div>
            )}
            <div>状态: {getStatusText(clip)}</div>
            <div style={{ marginTop: 4, fontSize: '12px', opacity: 0.8 }}>
              点击跳转到此时间播放
            </div>
          </div>
        }
        placement="top"
      >
        <Button
          size="small"
          type={isActive ? "primary" : "default"}
          ghost={!isActive}
          onClick={() => handleClipClick(clip)}
          className="clip-button"
          style={{
            borderColor: getStatusColor(clip.status),
            color: isActive ? undefined : getStatusColor(clip.status),
            minWidth: '80px',
            height: '32px'
          }}
          icon={<PlayCircleOutlined />}
        >
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <span style={{ fontSize: '11px', lineHeight: '12px' }}>
              {formatTime(clip.time)}
            </span>
            <span style={{ fontSize: '10px', lineHeight: '10px', opacity: 0.8 }}>
              {getStatusText(clip)}
            </span>
          </div>
        </Button>
      </Tooltip>
    );
  };

  const renderClipCard = (clip: ClipItem, index: number) => {
    return (
      <Card
        key={index}
        size="small"
        hoverable
        onClick={() => handleClipClick(clip)}
        className="clip-card"
        style={{
          marginBottom: 8,
          cursor: 'pointer',
          borderColor: getStatusColor(clip.status)
        }}
        styles={{ body: { padding: '8px 12px' } }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ flex: 1 }}>
            <Space size={4}>
              <ClockCircleOutlined style={{ color: '#1890ff' }} />
              <span style={{ fontWeight: 500 }}>{formatTime(clip.time)}</span>
              {clip.itemName && (
                <>
                  <ShoppingOutlined style={{ color: '#52c41a' }} />
                  <span>{clip.itemName}</span>
                </>
              )}
            </Space>
            {clip.text && (
              <div style={{ 
                marginTop: 4, 
                fontSize: '12px', 
                color: '#666',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {clip.text}
              </div>
            )}
          </div>
          <div style={{ marginLeft: 8 }}>
            <Tag 
              color={getStatusColor(clip.status)}
              style={{ margin: 0 }}
            >
              {getStatusText(clip)}
            </Tag>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <TagOutlined />
          {title}
          <span style={{ fontSize: '12px', color: '#666', fontWeight: 'normal' }}>
            ({clips.length} 个片段)
          </span>
        </div>
      }
      size="small"
      className={`clip-list-container ${className || ''}`}
      style={style}
    >
      {clips.length === 0 ? (
        <Empty 
          description="暂无视频片段"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ margin: '20px 0' }}
        />
      ) : (
        <>
          {/* Button style for quick access */}
          <div style={{ marginBottom: 16 }}>
            <div style={{ 
              fontSize: '12px', 
              color: '#666', 
              marginBottom: 8,
              fontWeight: 500
            }}>
              快速跳转:
            </div>
            <div style={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: 8 
            }}>
              {clips.map((clip, index) => renderClipButton(clip, index))}
            </div>
          </div>

          {/* Card style for detailed view */}
          <div>
            <div style={{ 
              fontSize: '12px', 
              color: '#666', 
              marginBottom: 8,
              fontWeight: 500
            }}>
              详细信息:
            </div>
            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
              {clips.map((clip, index) => renderClipCard(clip, index))}
            </div>
          </div>
        </>
      )}
    </Card>
  );
};

export default ClipList;
