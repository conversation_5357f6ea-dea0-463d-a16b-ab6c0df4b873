# 时间轴加载问题调试指南

## 问题描述
标记数据保存后，重新读取时第一个片段没有正确显示为已标记状态，但浏览器控制台显示API响应是完整的。

## 根本原因分析
这是一个典型的**竞态条件（Race Condition）**问题：

1. **时间轴初始化**：当`videoRecord`变化时触发
2. **历史标记加载**：当`visible`变为true时触发

这两个操作可能同时执行，导致：
- 历史标记加载完成 → 时间轴状态更新
- 时间轴重新初始化 → 覆盖历史标记状态

## 修复方案

### 1. 添加状态控制
```typescript
const [timelineInitialized, setTimelineInitialized] = useState(false);
```

### 2. 分离初始化和加载逻辑
```typescript
// 时间轴初始化
useEffect(() => {
  if (videoRecord?.duration) {
    // 初始化时间轴片段
    setTimelineSegments(segments);
    setTimelineInitialized(true);
  }
}, [videoRecord]);

// 历史标记加载
useEffect(() => {
  if (timelineInitialized && visible && videoRecord?.id) {
    loadHistoricalMarkings();
  }
}, [timelineInitialized, visible, videoRecord?.id]);
```

### 3. 增强调试信息
添加了详细的console.log来跟踪：
- 时间轴初始化过程
- 历史标记加载过程
- 数据合并过程
- 最终状态

## 调试步骤

### 1. 打开浏览器开发者工具
- 按F12打开开发者工具
- 切换到Console标签

### 2. 重现问题
1. 选择一个已有标记的视频
2. 打开商品识别界面
3. 观察控制台输出

### 3. 检查关键日志

#### 时间轴初始化日志
```
Initializing timeline for video: 123 duration: 60000
Generated timeline segments: 6
```

#### 历史标记加载日志
```
Timeline initialized and modal visible, loading historical markings...
Loading historical markings for videoId: 123
Found historical markings: [...]
Current timeline segments before processing: [...]
```

#### 数据合并日志
```
Processing marking 1/2: {startTs: 0, endTs: 10000, itemId: "789"}
Looking for segment with startTime=0, endTime=10000, found at index: 0
Updating existing segment at index 0
Historical markings processed: 2 updated, 0 added
```

### 4. 验证修复效果

#### 预期行为
1. 时间轴先完成初始化
2. 然后加载历史标记
3. 历史标记正确合并到时间轴
4. 第一个片段正确显示为"已保存"状态

#### 检查要点
- [ ] 时间轴初始化完成后才开始加载历史标记
- [ ] 所有历史标记都能找到对应的时间轴片段
- [ ] 第一个片段的状态正确更新为"saved"
- [ ] 界面显示紫色背景和"已保存"文字

## 常见问题排查

### 问题1：时间戳不匹配
**症状**：日志显示"found at index: -1"
**原因**：保存和加载时的时间戳精度不一致
**解决**：检查时间戳的数据类型和精度

### 问题2：数据类型转换
**症状**：itemId比较失败
**原因**：数字和字符串类型混用
**解决**：确保数据类型一致性

### 问题3：异步操作时序
**症状**：历史标记加载在时间轴初始化之前
**原因**：useEffect依赖项设置不当
**解决**：使用timelineInitialized状态控制

## 测试验证

### 测试用例1：基础加载
1. 选择有历史标记的视频
2. 打开商品识别界面
3. 验证所有标记正确显示

### 测试用例2：第一个片段
1. 确保第一个片段（0-10秒）有标记
2. 重新打开界面
3. 验证第一个片段显示为已保存

### 测试用例3：多个片段
1. 保存多个片段的标记
2. 重新加载
3. 验证所有片段状态正确

### 测试用例4：边界情况
1. 测试视频开头和结尾的片段
2. 测试不规则时间范围的片段
3. 验证数据完整性

## 性能优化建议

### 1. 减少不必要的重新渲染
- 使用useCallback优化函数引用
- 合理设置useEffect依赖项

### 2. 优化数据结构
- 考虑使用Map存储时间轴片段
- 提高查找效率

### 3. 错误恢复机制
- 添加重试逻辑
- 提供手动刷新选项

## 后续监控

### 1. 添加性能监控
```typescript
const startTime = performance.now();
// ... 操作
const endTime = performance.now();
console.log(`Operation took ${endTime - startTime} ms`);
```

### 2. 添加错误上报
```typescript
try {
  // ... 操作
} catch (error) {
  console.error('Timeline loading error:', error);
  // 可以添加错误上报逻辑
}
```

### 3. 用户反馈收集
- 添加"数据加载异常"的反馈按钮
- 收集用户遇到的具体场景

---

**修复状态**: ✅ 已修复  
**测试状态**: 🔄 待验证  
**优先级**: 🔴 高优先级
