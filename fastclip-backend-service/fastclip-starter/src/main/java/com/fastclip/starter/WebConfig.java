package com.fastclip.starter;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Value("${video.hls.basePath}")
    private String hlsBasePath;

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        ByteArrayHttpMessageConverter arrayHttpMessageConverter = new ByteArrayHttpMessageConverter();
        arrayHttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM));
        converters.add(0, arrayHttpMessageConverter);
    }

    /**
     * Configure static resource handlers for HLS video files
     * This allows direct file access without going through a controller
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Configure HLS static resource handler
        registry.addResourceHandler("/video/hls/**")
                .addResourceLocations("file:" + hlsBasePath + "/")
                .setCachePeriod(3600) // Cache for 1 hour
                .resourceChain(true);

        // You can add more static resource handlers here if needed
        // For example, for thumbnails, covers, etc.
    }
}
