package com.fastclip.common.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class VideoMaterialClipDTO {
    private Long id;

    private Long itemId;

    private String itemName;

    private Long videoId;

    private Date videoDate;

    private String videoPath;

    private Long sellerId;

    private Integer duration;

    private Integer startTs;

    private Integer endTs;

    private Integer orginStartTs;

    private Integer originEndTs;

    private Integer scene;

    private Date createTime;

    private Date updateTime;
}