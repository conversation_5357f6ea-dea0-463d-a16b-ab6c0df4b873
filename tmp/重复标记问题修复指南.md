# 重复标记问题修复指南

## 🐛 问题描述
重新识别并重新标记时，没有正确覆盖之前的条目，而是意外新增了重复记录。

## 🔍 问题分析

### 根本原因
1. **后端逻辑错误**：使用 `(video_id, item_id, start_ts)` 来判断记录是否存在
2. **数据库索引设计问题**：唯一索引 `(video_id, item_id, start_ts)` 允许同一时间段有不同商品
3. **业务逻辑冲突**：同一时间段应该只能有一个商品标记

### 问题表现
从数据库数据可以看到：
```sql
-- 原始记录
| 12 | 12 | 122 | 6 | 10000 | 30000 | 40000 | 2025-07-30 20:58:00 | 2025-07-30 21:14:36 |
-- 重复记录（同一时间段，不同商品）
| 16 | 17 | 122 | 6 | 10000 | 30000 | 40000 | 2025-07-30 21:13:03 | 2025-07-30 21:13:03 |
```

## ✅ 修复方案

### 1. 后端逻辑修复

#### 修复前（错误逻辑）
```java
// 基于 (video_id, item_id, start_ts) 检查
example.createCriteria()
    .andVideoIdEqualTo(req.getVideoId())
    .andItemIdEqualTo(marking.getItemId())  // ❌ 问题所在
    .andStartTsEqualTo(marking.getStartTs());
```

#### 修复后（正确逻辑）
```java
// 基于 (video_id, start_ts, end_ts) 检查
example.createCriteria()
    .andVideoIdEqualTo(req.getVideoId())
    .andStartTsEqualTo(marking.getStartTs())
    .andEndTsEqualTo(marking.getEndTs());  // ✅ 基于时间段
```

### 2. 数据库索引修复

#### 当前索引（有问题）
```sql
unique index(`video_id`, `item_id`, `start_ts`)
```

#### 建议索引（正确）
```sql
unique index(`video_id`, `start_ts`, `end_ts`)
```

## 🔧 修复步骤

### 步骤1：清理重复数据

```sql
-- 1. 查看重复数据
SELECT video_id, start_ts, end_ts, COUNT(*) as count
FROM video_material_clip 
GROUP BY video_id, start_ts, end_ts 
HAVING COUNT(*) > 1;

-- 2. 删除重复记录（保留最新的）
DELETE t1 FROM video_material_clip t1
INNER JOIN video_material_clip t2 
WHERE t1.video_id = t2.video_id 
  AND t1.start_ts = t2.start_ts 
  AND t1.end_ts = t2.end_ts 
  AND t1.update_time < t2.update_time;
```

### 步骤2：修改数据库索引

```sql
-- 1. 删除旧索引
DROP INDEX `video_id` ON `video_material_clip`;

-- 2. 创建新索引
ALTER TABLE `video_material_clip` 
ADD UNIQUE INDEX `unique_video_time_segment` (`video_id`, `start_ts`, `end_ts`);
```

### 步骤3：验证修复效果

```sql
-- 验证没有重复的时间段
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT CONCAT(video_id, '-', start_ts, '-', end_ts)) as unique_segments
FROM video_material_clip;
```

## 🧪 测试验证

### 测试场景1：重新识别同一片段
1. 选择一个已标记的时间段
2. 重新识别为不同商品
3. 保存标记
4. **预期**：更新现有记录，不新增记录

### 测试场景2：新增标记
1. 选择一个未标记的时间段
2. 进行商品识别
3. 保存标记
4. **预期**：新增记录

### 测试场景3：数据库约束
1. 尝试手动插入重复时间段的记录
2. **预期**：数据库报唯一约束错误

## 📋 修复清单

### 后端代码修复 ✅
- [x] 修改 `VideoMaterialClipService.saveProductMarkings()` 方法
- [x] 改为基于时间段检查重复记录
- [x] 更新日志信息

### 数据库修复 🔄
- [ ] 清理现有重复数据
- [ ] 删除旧的唯一索引
- [ ] 创建新的唯一索引
- [ ] 验证数据完整性

### 测试验证 🔄
- [ ] 重新识别测试
- [ ] 新增标记测试
- [ ] 数据库约束测试

## ⚠️ 注意事项

### 数据备份
在执行数据库修改前，请先备份：
```sql
CREATE TABLE video_material_clip_backup AS 
SELECT * FROM video_material_clip;
```

### 停机时间
- 索引修改可能需要短暂停机
- 建议在低峰期执行
- 预计停机时间：< 5分钟

### 回滚方案
如果出现问题，可以回滚：
```sql
-- 恢复旧索引
DROP INDEX `unique_video_time_segment` ON `video_material_clip`;
ALTER TABLE `video_material_clip` 
ADD UNIQUE INDEX `video_id` (`video_id`, `item_id`, `start_ts`);

-- 恢复数据（如果需要）
-- TRUNCATE video_material_clip;
-- INSERT INTO video_material_clip SELECT * FROM video_material_clip_backup;
```

## 🎯 预期效果

### 修复后的行为
1. **重新识别**：同一时间段的新识别结果会覆盖旧记录
2. **数据一致性**：每个时间段只有一个商品标记
3. **性能优化**：基于时间段的查询更高效

### 日志输出示例
```
Replacing existing marking: id=12, oldItemId=12, newItemId=17, timeSegment=30000-40000
Successfully updated marking: id=12, videoId=122, newItemId=17, timeSegment=30000-40000
```

## 📚 相关文档
- [数据库清理脚本](./clean-duplicate-markings.sql)
- [索引修复脚本](./fix-video-material-clip-unique-index.sql)
- [后端代码修改](../fastclip-backend-service/fastclip-service/src/main/java/com/fastclip/service/video/VideoMaterialClipService.java)

---

**修复状态**: 🔄 进行中  
**优先级**: 🔴 高优先级  
**影响范围**: 商品标记保存逻辑  
**风险等级**: 🟡 中等风险（需要数据库修改）
