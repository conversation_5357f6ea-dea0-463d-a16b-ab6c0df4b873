# 商品标记保存和加载API设计文档

## 1. 保存标记结果API

### 接口信息
- **URL**: `/api/fastclip/video/saveProductMarkings`
- **Method**: POST
- **Content-Type**: application/json

### 请求参数 (SaveProductMarkingsReq)
```json
{
  "videoId": 123,
  "sellerId": 456,
  "markings": [
    {
      "itemId": 789,
      "startTs": 10000,
      "endTs": 20000,
      "duration": 10000
    },
    {
      "itemId": 790,
      "startTs": 30000,
      "endTs": 40000,
      "duration": 10000
    }
  ]
}
```

### 请求参数说明
- `videoId`: 视频ID (必填)
- `sellerId`: 达人ID (必填)
- `markings`: 标记片段数组 (必填)
  - `itemId`: 商品ID (必填)
  - `startTs`: 开始时间，单位ms (必填)
  - `endTs`: 结束时间，单位ms (必填)
  - `duration`: 片段时长，单位ms (必填)

### 响应格式
```json
{
  "resultCode": 0,
  "resultDesc": "保存成功",
  "result": {
    "savedCount": 2,
    "updatedCount": 0,
    "totalCount": 2
  }
}
```

### 响应参数说明
- `savedCount`: 新保存的标记数量
- `updatedCount`: 更新的标记数量
- `totalCount`: 总处理的标记数量

## 2. 查询历史标记结果API

### 接口信息
- **URL**: `/api/fastclip/video/getProductMarkings`
- **Method**: POST
- **Content-Type**: application/json

### 请求参数 (GetProductMarkingsReq)
```json
{
  "videoId": 123
}
```

### 请求参数说明
- `videoId`: 视频ID (必填)

### 响应格式
```json
{
  "resultCode": 0,
  "resultDesc": "查询成功",
  "result": [
    {
      "id": 1,
      "itemId": 789,
      "itemName": "商品名称1",
      "videoId": 123,
      "sellerId": 456,
      "startTs": 10000,
      "endTs": 20000,
      "duration": 10000,
      "createTime": "2024-01-01 10:00:00",
      "updateTime": "2024-01-01 10:00:00"
    },
    {
      "id": 2,
      "itemId": 790,
      "itemName": "商品名称2",
      "videoId": 123,
      "sellerId": 456,
      "startTs": 30000,
      "endTs": 40000,
      "duration": 10000,
      "createTime": "2024-01-01 10:01:00",
      "updateTime": "2024-01-01 10:01:00"
    }
  ]
}
```

### 响应参数说明
- `id`: 标记记录ID
- `itemId`: 商品ID
- `itemName`: 商品名称 (从商品表关联查询)
- `videoId`: 视频ID
- `sellerId`: 达人ID
- `startTs`: 开始时间，单位ms
- `endTs`: 结束时间，单位ms
- `duration`: 片段时长，单位ms
- `createTime`: 创建时间
- `updateTime`: 更新时间

## 3. 前端数据结构

### 时间轴片段数据结构 (TimelineSegmentData)
```typescript
interface TimelineSegmentData {
  startTime: number;
  endTime: number;
  status: 'pending' | 'processing' | 'completed' | 'error' | 'saved';
  productType?: number;
  confidence?: number;
  itemId?: string;
  itemName?: string;
  // 新增字段标识已保存状态
  isSaved?: boolean;
  savedId?: number; // 保存记录的ID
  // Manual selection support
  isManuallySelected?: boolean;
  originalItemId?: string;
  originalItemName?: string;
  originalConfidence?: number;
}
```

### 保存请求数据结构
```typescript
interface SaveMarkingsRequest {
  videoId: number;
  sellerId: number;
  markings: Array<{
    itemId: number;
    startTs: number;
    endTs: number;
    duration: number;
  }>;
}
```

### 查询响应数据结构
```typescript
interface ProductMarking {
  id: number;
  itemId: number;
  itemName: string;
  videoId: number;
  sellerId: number;
  startTs: number;
  endTs: number;
  duration: number;
  createTime: string;
  updateTime: string;
}
```

## 4. 业务逻辑说明

### 保存逻辑
1. 前端收集当前时间轴上所有已识别的商品片段
2. 过滤掉无效的片段（没有itemId的片段）
3. 发送保存请求到后端
4. 后端使用 `INSERT ... ON DUPLICATE KEY UPDATE` 处理重复数据
5. 返回保存结果统计

### 查询逻辑
1. 根据videoId查询video_material_clip表
2. 关联查询商品表获取商品名称
3. 按时间顺序返回结果
4. 前端将查询结果合并到时间轴显示

### 状态管理
- `saved`: 已保存到数据库的片段
- `completed`: 刚完成识别但未保存的片段
- 已保存片段支持重新识别和修改
- 保存时会覆盖相同时间段的旧标记

## 5. 错误处理

### 常见错误码
- `1001`: 参数验证失败
- `1002`: 视频不存在
- `1003`: 达人不存在
- `1004`: 数据库操作失败
- `1005`: 商品不存在

### 错误响应格式
```json
{
  "resultCode": 1001,
  "resultDesc": "参数验证失败：videoId不能为空",
  "result": null
}
```
