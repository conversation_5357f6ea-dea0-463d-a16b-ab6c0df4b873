-- 修复video_material_clip表的唯一索引
-- 问题：当前索引允许同一时间段有不同商品，导致重复数据
-- 解决：改为基于时间段的唯一索引

-- 1. 首先清理重复数据
-- 查看当前重复数据
SELECT video_id, start_ts, end_ts, COUNT(*) as count
FROM video_material_clip 
GROUP BY video_id, start_ts, end_ts 
HAVING COUNT(*) > 1;

-- 删除重复数据，保留最新的记录（基于update_time）
DELETE t1 FROM video_material_clip t1
INNER JOIN video_material_clip t2 
WHERE t1.video_id = t2.video_id 
  AND t1.start_ts = t2.start_ts 
  AND t1.end_ts = t2.end_ts 
  AND t1.update_time < t2.update_time;

-- 2. 删除旧的唯一索引
DROP INDEX `video_id` ON `video_material_clip`;

-- 3. 创建新的唯一索引（基于时间段）
ALTER TABLE `video_material_clip` 
ADD UNIQUE INDEX `unique_video_time_segment` (`video_id`, `start_ts`, `end_ts`);

-- 4. 验证索引创建成功
SHOW INDEX FROM `video_material_clip` WHERE Key_name = 'unique_video_time_segment';

-- 5. 验证数据完整性
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT CONCAT(video_id, '-', start_ts, '-', end_ts)) as unique_segments
FROM video_material_clip;

-- 如果total_records = unique_segments，说明没有重复的时间段
