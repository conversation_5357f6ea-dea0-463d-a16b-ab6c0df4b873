#!/bin/bash

# HLS视频转换脚本
# 用法: ./convert_to_hls.sh <视频ID> <输入视频路径> [质量选项]
# 示例: ./convert_to_hls.sh 123 /data/app/seller/video.mp4 high

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "HLS视频转换脚本"
    echo ""
    echo "用法:"
    echo "  $0 <视频ID> <输入视频路径> [质量选项]"
    echo ""
    echo "参数:"
    echo "  视频ID          数字ID，用于生成输出路径"
    echo "  输入视频路径    要转换的视频文件完整路径"
    echo "  质量选项        可选: low, medium, high, ultra (默认: medium)"
    echo ""
    echo "示例:"
    echo "  $0 123 /data/app/seller/video.mp4"
    echo "  $0 456 /path/to/video.mp4 high"
    echo ""
    echo "质量选项说明:"
    echo "  low     - 2000k视频码率, 64k音频码率  (适合网络较差环境)"
    echo "  medium  - 4000k视频码率, 128k音频码率 (默认，平衡质量和大小)"
    echo "  high    - 6000k视频码率, 192k音频码率 (高质量)"
    echo "  ultra   - 8000k视频码率, 256k音频码率 (超高质量)"
}

# 参数检查
if [ $# -lt 2 ] || [ $# -gt 3 ]; then
    log_error "参数数量错误"
    show_help
    exit 1
fi

if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 参数赋值
VIDEO_ID=$1
INPUT_PATH=$2
QUALITY=${3:-medium}

# 配置路径
BASE_PATH="/data/app/douyin/hls"
OUTPUT_DIR="${BASE_PATH}/${VIDEO_ID}"
OUTPUT_PLAYLIST="${OUTPUT_DIR}/playlist.m3u8"

# 质量配置
case $QUALITY in
    low)
        VIDEO_BITRATE="2000k"
        AUDIO_BITRATE="64k"
        PRESET="veryfast"
        ;;
    medium)
        VIDEO_BITRATE="4000k"
        AUDIO_BITRATE="128k"
        PRESET="veryfast"
        ;;
    high)
        VIDEO_BITRATE="6000k"
        AUDIO_BITRATE="192k"
        PRESET="fast"
        ;;
    ultra)
        VIDEO_BITRATE="8000k"
        AUDIO_BITRATE="256k"
        PRESET="medium"
        ;;
    *)
        log_error "无效的质量选项: $QUALITY"
        log_info "支持的选项: low, medium, high, ultra"
        exit 1
        ;;
esac

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v ffmpeg &> /dev/null; then
        log_error "FFmpeg未安装或不在PATH中"
        log_info "请安装FFmpeg: sudo apt install ffmpeg"
        exit 1
    fi
    
    log_success "FFmpeg已安装: $(ffmpeg -version | head -n1)"
}

# 验证输入
validate_input() {
    log_info "验证输入参数..."
    
    # 检查视频ID是否为数字
    if ! [[ "$VIDEO_ID" =~ ^[0-9]+$ ]]; then
        log_error "视频ID必须是数字: $VIDEO_ID"
        exit 1
    fi
    
    # 检查输入文件
    if [ ! -f "$INPUT_PATH" ]; then
        log_error "输入文件不存在: $INPUT_PATH"
        exit 1
    fi
    
    # 检查文件是否为视频文件
    if ! ffprobe -v quiet -select_streams v:0 -show_entries stream=codec_type -of csv=p=0 "$INPUT_PATH" | grep -q video; then
        log_error "输入文件不是有效的视频文件: $INPUT_PATH"
        exit 1
    fi
    
    log_success "输入验证通过"
}

# 创建输出目录
create_output_dir() {
    log_info "创建输出目录..."
    
    if [ -d "$OUTPUT_DIR" ]; then
        log_warning "输出目录已存在，将覆盖现有文件: $OUTPUT_DIR"
        rm -rf "$OUTPUT_DIR"/*
    else
        mkdir -p "$OUTPUT_DIR"
    fi
    
    # 检查目录权限
    if [ ! -w "$OUTPUT_DIR" ]; then
        log_error "没有写入权限: $OUTPUT_DIR"
        log_info "请检查目录权限或使用sudo运行"
        exit 1
    fi
    
    log_success "输出目录准备完成: $OUTPUT_DIR"
}

# 获取视频信息
get_video_info() {
    log_info "获取视频信息..."
    
    DURATION=$(ffprobe -v quiet -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "$INPUT_PATH")
    DURATION_INT=$(printf "%.0f" "$DURATION")
    
    RESOLUTION=$(ffprobe -v quiet -select_streams v:0 -show_entries stream=width,height -of csv=s=x:p=0 "$INPUT_PATH")
    
    SIZE=$(du -h "$INPUT_PATH" | cut -f1)
    
    log_info "视频时长: ${DURATION_INT}秒"
    log_info "视频分辨率: $RESOLUTION"
    log_info "文件大小: $SIZE"
}

# 执行转换
convert_video() {
    log_info "开始转换视频..."
    log_info "质量设置: $QUALITY (视频: $VIDEO_BITRATE, 音频: $AUDIO_BITRATE)"
    
    # 构建FFmpeg命令
    FFMPEG_CMD=(
        ffmpeg
        -i "$INPUT_PATH"
        -preset "$PRESET"
        -g 48
        -sc_threshold 0
        -c:v libx264
        -b:v "$VIDEO_BITRATE"
        -maxrate "$VIDEO_BITRATE"
        -bufsize "$((${VIDEO_BITRATE%k} * 2))k"
        -profile:v main
        -level 4.0
        -c:a aac
        -b:a "$AUDIO_BITRATE"
        -ac 2
        -ar 44100
        -hls_time 4
        -hls_playlist_type vod
        -hls_segment_filename "${OUTPUT_DIR}/segment_%03d.ts"
        -hls_flags delete_segments+append_list
        "$OUTPUT_PLAYLIST"
        -y
    )
    
    # 显示命令（用于调试）
    log_info "执行命令: ${FFMPEG_CMD[*]}"
    
    # 执行转换并显示进度
    "${FFMPEG_CMD[@]}" 2>&1 | while IFS= read -r line; do
        if [[ $line == *"time="* ]]; then
            echo -ne "\r转换进度: $line"
        fi
    done
    
    echo  # 换行
    
    # 检查转换结果
    if [ $? -eq 0 ] && [ -f "$OUTPUT_PLAYLIST" ]; then
        log_success "视频转换完成!"
    else
        log_error "视频转换失败!"
        exit 1
    fi
}

# 验证输出
validate_output() {
    log_info "验证输出文件..."
    
    # 检查播放列表文件
    if [ ! -f "$OUTPUT_PLAYLIST" ]; then
        log_error "播放列表文件未生成: $OUTPUT_PLAYLIST"
        exit 1
    fi
    
    # 统计片段文件数量
    SEGMENT_COUNT=$(ls -1 "${OUTPUT_DIR}"/segment_*.ts 2>/dev/null | wc -l)
    
    if [ "$SEGMENT_COUNT" -eq 0 ]; then
        log_error "没有生成视频片段文件"
        exit 1
    fi
    
    # 计算输出目录大小
    OUTPUT_SIZE=$(du -sh "$OUTPUT_DIR" | cut -f1)
    
    log_success "生成了 $SEGMENT_COUNT 个视频片段"
    log_success "输出目录大小: $OUTPUT_SIZE"
}

# 显示结果
show_result() {
    log_success "=== 转换完成 ==="
    echo ""
    log_info "视频ID: $VIDEO_ID"
    log_info "输入文件: $INPUT_PATH"
    log_info "输出目录: $OUTPUT_DIR"
    log_info "播放列表: $OUTPUT_PLAYLIST"
    echo ""
    log_info "访问URL:"
    log_info "  http://localhost:8078/api/fastclip/video/hls/${VIDEO_ID}/playlist.m3u8"
    echo ""
    log_info "测试命令:"
    log_info "  curl http://localhost:8078/api/fastclip/video/hls/${VIDEO_ID}/playlist.m3u8"
    echo ""
    
    # 显示播放列表内容预览
    if [ -f "$OUTPUT_PLAYLIST" ]; then
        log_info "播放列表内容预览:"
        head -n 10 "$OUTPUT_PLAYLIST" | sed 's/^/  /'
        if [ $(wc -l < "$OUTPUT_PLAYLIST") -gt 10 ]; then
            echo "  ..."
        fi
    fi
}

# 主函数
main() {
    log_info "开始HLS视频转换..."
    log_info "视频ID: $VIDEO_ID"
    log_info "输入路径: $INPUT_PATH"
    log_info "质量设置: $QUALITY"
    echo ""
    
    check_dependencies
    validate_input
    create_output_dir
    get_video_info
    convert_video
    validate_output
    show_result
    
    log_success "所有操作完成!"
}

# 执行主函数
main
