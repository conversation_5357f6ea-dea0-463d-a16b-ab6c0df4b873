# 视频播放器功能测试指南

## 功能概述

本次为商品识别界面添加了基于HLS的视频播放器功能，实现视频预览和时间轴联动：

### 新增功能
1. ✅ **HLS视频播放器** - 基于Video.js的专业视频播放器，位于时间轴上方
2. ✅ **时间轴联动** - 点击时间轴片段时，视频播放器自动跳转到对应位置
3. ✅ **视频预览** - 用户可以预览视频内容，辅助商品识别结果确认
4. ✅ **简洁布局** - 播放器紧凑布局，不影响原有商品识别工作流程

### 技术实现
- **前端**: Video.js + React + TypeScript
- **后端**: HLS视频流配置 (application.yml)
- **样式**: 自定义CSS样式，与Ant Design主题一致

## 测试准备

### 环境要求
- 前端服务正常运行 (端口3000)
- 后端服务正常运行 (端口8078)
- 数据库连接正常
- WebSocket连接正常
- HLS视频文件准备就绪

### 测试数据
- 准备一个有效的视频记录
- 确保视频有对应的HLS文件 (.m3u8)
- 准备一些商品数据用于识别

## 详细测试步骤

### 1. 界面布局测试

#### 1.1 打开商品识别界面
**测试步骤：**
1. 打开视频素材管理页面
2. 选择一个视频，点击"商品标记"
3. 等待商品识别界面加载完成

**预期结果：**
- ✅ 界面正常显示，包含视频信息卡片
- ✅ 新增视频播放器区域 (位于时间轴上方，全宽布局)
- ✅ 原有时间轴正常显示在播放器下方
- ✅ WebSocket连接状态显示为"已连接"

#### 1.2 响应式布局验证
**测试步骤：**
1. 调整浏览器窗口大小
2. 测试不同分辨率下的显示效果

**预期结果：**
- ✅ 视频播放器自适应容器大小
- ✅ 播放器高度固定为300px，宽度自适应
- ✅ 移动端适配正常

### 2. 视频播放器功能测试

#### 2.1 HLS视频加载
**测试步骤：**
1. 确认视频播放器显示正常
2. 检查HLS视频源是否正确加载
3. 点击播放按钮

**预期结果：**
- ✅ 视频播放器正常初始化
- ✅ HLS视频流正常加载
- ✅ 播放控件响应正常
- ✅ 进度条显示正确

#### 2.2 播放控制功能
**测试步骤：**
1. 测试播放/暂停功能
2. 测试音量控制
3. 测试全屏功能
4. 测试播放速度调节

**预期结果：**
- ✅ 所有播放控件正常工作
- ✅ 快捷键支持正常
- ✅ 全屏模式正常进入/退出

### 3. 时间轴联动测试

#### 3.1 时间轴点击跳转
**测试步骤：**
1. 完成商品识别，确保时间轴有识别结果
2. 点击时间轴上的任意片段
3. 观察视频播放器是否跳转到对应时间

**预期结果：**
- ✅ 点击时间轴片段后视频立即跳转到对应时间
- ✅ 跳转后视频自动开始播放
- ✅ 时间显示准确，误差在1秒内
- ✅ 跳转过程流畅，无卡顿

#### 3.2 播放器时间更新
**测试步骤：**
1. 在视频播放器中播放视频
2. 观察播放器时间更新是否正常
3. 手动拖拽播放器进度条

**预期结果：**
- ✅ 播放器时间实时更新
- ✅ 进度条拖拽响应正常
- ✅ 时间显示格式正确

### 4. 错误处理测试

#### 4.1 视频加载失败
**测试步骤：**
1. 使用无效的视频ID
2. 观察错误处理

**预期结果：**
- ✅ 显示友好的错误提示
- ✅ 不影响其他功能正常使用

#### 4.2 网络连接问题
**测试步骤：**
1. 模拟网络中断
2. 观察播放器行为

**预期结果：**
- ✅ 显示网络错误提示
- ✅ 重连后恢复正常

## 性能测试

### 1. 加载性能
- ✅ 视频播放器初始化时间 < 2秒
- ✅ HLS视频首帧显示时间 < 3秒
- ✅ 时间轴点击响应时间 < 500ms

### 2. 内存使用
- ✅ 播放器正常释放资源
- ✅ 组件卸载时清理事件监听
- ✅ 长时间使用无内存泄漏

## 兼容性测试

### 浏览器支持
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### 移动端支持
- ✅ iOS Safari
- ✅ Android Chrome
- ✅ 触摸操作正常

## 已知问题和限制

### 当前限制
1. **HLS文件依赖**: 需要预先准备HLS格式的视频文件
2. **实时转码**: 暂未实现实时视频转HLS功能
3. **进度条标记**: 暂未在播放器进度条上显示片段标记

### 后续改进方向
1. 集成FFmpeg实现实时HLS转码
2. 在播放器进度条上添加片段标记显示
3. 支持更多视频格式
4. 添加视频缩略图预览
5. 支持键盘快捷键控制

## 部署注意事项

### 后端配置
确保 `application.yml` 中的HLS配置正确：
```yaml
video:
  hls:
    basePath: ${BASE_DATA_PATH:/data/app}/douyin/hls/
    # urlPrefix: ${HLS_URL_PREFIX:http://localhost:8078/api/fastclip/video/hls/}
    segmentDuration: 4
    playlistType: vod
```

### 前端依赖
确保安装了必要的npm包：
```bash
npm install video.js
```

### 静态资源
确保HLS视频文件可通过HTTP访问，路径格式：
```
/api/fastclip/video/hls/{videoId}/playlist.m3u8
```

## 测试完成标准

- [ ] 所有界面元素正常显示
- [ ] 视频播放功能完全正常
- [ ] 时间轴点击跳转功能正常
- [ ] 播放器与时间轴联动正常
- [ ] 错误处理机制有效
- [ ] 性能指标达标
- [ ] 兼容性测试通过
- [ ] 用户体验良好

## 联系信息

如遇到问题，请联系开发团队或查看相关文档：
- 技术文档: `/tmp/` 目录下的相关文档
- 代码位置: `fastclip-front-service/src/pages/Management/VideoMaterialList/components/`
- 主要文件: `VideoPlayer.tsx`, `ProductMarkingModal.tsx`
