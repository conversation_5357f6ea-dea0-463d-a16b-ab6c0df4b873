# HLS静态资源配置指南

## 🎯 配置概述

采用Spring Boot静态资源配置方式来serve HLS文件，这比Controller方式更简单、高效。

## ✅ 配置实现

### 1. 认证忽略配置

**文件**: `application.yml`
```yaml
auth:
  ignoreList: login,videoPlay,upload,undefined,douyin/auth,downloadWithToken,ws/product-recognition,video/hls
```

### 2. 静态资源配置

**文件**: `WebConfig.java`
```java
@Override
public void addResourceHandlers(ResourceHandlerRegistry registry) {
    // Configure HLS static resource handler
    registry.addResourceHandler("/video/hls/**")
            .addResourceLocations("file:" + hlsBasePath + "/")
            .setCachePeriod(3600) // Cache for 1 hour
            .resourceChain(true);
}
```

**配置说明**:
- **URL模式**: `/video/hls/**` 匹配所有HLS相关请求
- **文件位置**: `file:/data/app/douyin/hls/` (基于配置的basePath)
- **缓存策略**: 1小时缓存，适合视频文件
- **资源链**: 启用资源链以支持版本控制和压缩

## 🔗 URL映射关系

### 访问路径映射
```
前端请求: /api/fastclip/video/hls/{videoId}/playlist.m3u8
实际文件: /data/app/douyin/hls/{videoId}/playlist.m3u8

前端请求: /api/fastclip/video/hls/{videoId}/segment_000.ts  
实际文件: /data/app/douyin/hls/{videoId}/segment_000.ts
```

### 示例
```
URL: http://localhost:8078/api/fastclip/video/hls/123/playlist.m3u8
文件: /data/app/douyin/hls/123/playlist.m3u8

URL: http://localhost:8078/api/fastclip/video/hls/123/segment_000.ts
文件: /data/app/douyin/hls/123/segment_000.ts
```

## 🧪 测试验证

### 1. 基础连通性测试

```bash
# 测试播放列表文件
curl -v http://localhost:8078/api/fastclip/video/hls/123/playlist.m3u8

# 测试视频片段文件
curl -v http://localhost:8078/api/fastclip/video/hls/123/segment_000.ts
```

**预期结果**:
- HTTP状态码: 200
- 正确的Content-Type头
- 文件内容正常返回

### 2. 文件系统验证

```bash
# 检查HLS基础目录
ls -la /data/app/douyin/hls/

# 检查特定视频目录
ls -la /data/app/douyin/hls/123/

# 验证文件权限
stat /data/app/douyin/hls/123/playlist.m3u8
```

### 3. 前端集成测试

1. 打开商品识别界面
2. 选择视频ID为123的视频
3. 观察视频播放器加载情况
4. 检查浏览器Network标签的请求状态

## 🔧 优势对比

### 静态资源方式 vs Controller方式

| 特性 | 静态资源 | Controller |
|------|----------|------------|
| **性能** | ⭐⭐⭐⭐⭐ 直接文件访问 | ⭐⭐⭐ 需要Java处理 |
| **内存使用** | ⭐⭐⭐⭐⭐ 低内存占用 | ⭐⭐⭐ 较高内存占用 |
| **配置复杂度** | ⭐⭐⭐⭐⭐ 简单配置 | ⭐⭐ 需要编写Controller |
| **缓存支持** | ⭐⭐⭐⭐⭐ 原生HTTP缓存 | ⭐⭐⭐ 需要手动实现 |
| **扩展性** | ⭐⭐⭐ 功能有限 | ⭐⭐⭐⭐⭐ 高度可定制 |
| **调试能力** | ⭐⭐ 有限的调试信息 | ⭐⭐⭐⭐⭐ 详细日志和调试 |

### 静态资源方式的优势

1. **高性能**: 直接由Web服务器处理，无需Java应用层处理
2. **低资源消耗**: 不占用应用服务器资源
3. **原生缓存**: 自动支持HTTP缓存机制
4. **简单维护**: 配置简单，无需额外代码
5. **标准化**: 符合Web静态资源的标准做法

## ⚠️ 注意事项

### 1. 文件权限
```bash
# 确保应用有读取权限
sudo chown -R fastclip:fastclip /data/app/douyin/hls/
sudo chmod -R 755 /data/app/douyin/hls/
```

### 2. 路径配置
确保 `application.yml` 中的路径配置正确：
```yaml
video:
  hls:
    basePath: ${BASE_DATA_PATH:/data/app}/douyin/hls/
```

### 3. 缓存策略
- 当前设置为1小时缓存
- 如需实时更新，可以调整 `setCachePeriod(0)`
- 生产环境建议使用CDN进一步优化

### 4. 安全考虑
- 静态资源配置已经包含路径安全检查
- 建议在生产环境中添加访问频率限制
- 考虑添加文件类型白名单

## 🚀 部署步骤

### 1. 重启应用
```bash
# 重启Spring Boot应用以加载新配置
sudo systemctl restart fastclip-backend
```

### 2. 验证配置
```bash
# 测试静态资源访问
curl -I http://localhost:8078/api/fastclip/video/hls/123/playlist.m3u8

# 检查响应头
curl -v http://localhost:8078/api/fastclip/video/hls/123/playlist.m3u8 2>&1 | grep -E "(HTTP|Content-Type|Cache-Control)"
```

### 3. 前端测试
1. 清除浏览器缓存
2. 重新打开商品识别界面
3. 验证视频播放功能

## 🔍 故障排除

### 常见问题

#### 1. 404 Not Found
**检查项**:
- 文件是否存在: `ls -la /data/app/douyin/hls/{videoId}/`
- 路径配置是否正确
- 应用是否重启

#### 2. 403 Forbidden  
**检查项**:
- 文件权限: `ls -la /data/app/douyin/hls/`
- 目录权限: `stat /data/app/douyin/hls/`
- SELinux状态: `sestatus`

#### 3. 401 Unauthorized
**检查项**:
- 认证忽略列表是否包含 `video/hls`
- 应用配置是否重新加载

### 调试命令

```bash
# 检查Spring Boot静态资源映射
curl -v http://localhost:8078/api/fastclip/video/hls/

# 检查文件系统
find /data/app/douyin/hls/ -name "*.m3u8" -ls

# 检查应用日志
tail -f /tmp/logs/fastclip.log | grep -i hls
```

## 📈 性能优化建议

### 1. CDN集成
考虑将HLS文件部署到CDN：
```yaml
video:
  hls:
    basePath: /data/app/douyin/hls/
    cdnUrl: https://cdn.example.com/hls/
```

### 2. 压缩配置
在WebConfig中启用压缩：
```java
registry.addResourceHandler("/video/hls/**")
        .addResourceLocations("file:" + hlsBasePath + "/")
        .setCachePeriod(3600)
        .resourceChain(true)
        .addResolver(new GzipResourceResolver());
```

### 3. 监控指标
- 文件访问频率
- 缓存命中率
- 响应时间
- 磁盘I/O使用率

## ✅ 总结

静态资源配置方式为HLS文件访问提供了：
- ✅ 更高的性能
- ✅ 更简单的配置
- ✅ 更低的资源消耗
- ✅ 标准的HTTP缓存支持

这种方式特别适合视频文件这种大文件的静态内容分发场景。
