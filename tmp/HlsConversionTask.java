package com.fastclip.service.schedule;

import com.fastclip.common.model.entity.VideoMaterial;
import com.fastclip.dao.VideoMaterialMapper;
import com.fastclip.service.video.HlsConversionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * HLS视频转换定时任务
 * 自动将上传的视频转换为HLS格式，支持视频播放器播放
 */
@Component
@Slf4j
public class HlsConversionTask {

    @Autowired
    private VideoMaterialMapper videoMaterialMapper;

    @Autowired
    private HlsConversionService hlsConversionService;

    @Value("${schedule.hls_conversion_enabled:true}")
    private boolean scheduleEnabled;

    /**
     * 每30秒检查一次是否有需要转换的视频
     * 只处理状态为正常且未转换HLS的视频
     */
    @Scheduled(fixedDelay = 30000)
    public void convertVideoToHls() {
        try {
            if (!scheduleEnabled) {
                return;
            }

            // 随机睡眠0-5秒，防止多实例冲突
            try {
                Thread.sleep((int) (5000 * Math.random()));
            } catch (Exception e) {
                // ignore
            }

            // 查询需要转换HLS的视频
            VideoMaterialExample example = new VideoMaterialExample();
            example.createCriteria()
                    .andIsHlsConvertedEqualTo(false)  // 未转换HLS
                    .andStatusEqualTo(1)              // 状态正常
                    .andPathIsNotNull();              // 路径不为空
            example.setOrderByClause("create_time ASC");
            
            RowBounds rowBounds = new RowBounds(0, 1);
            List<VideoMaterial> videoMaterials = videoMaterialMapper.selectByExampleWithRowbounds(example, rowBounds);
            
            if (CollectionUtils.isEmpty(videoMaterials)) {
                return;
            }

            VideoMaterial videoMaterial = videoMaterials.get(0);
            log.info("开始转换视频到HLS格式, videoId={}, path={}", 
                    videoMaterial.getId(), videoMaterial.getPath());

            try {
                // 标记为转换中，避免重复处理
                videoMaterial.setIsHlsConverting(true);
                videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);

                // 执行HLS转换
                boolean success = hlsConversionService.convertToHls(
                        videoMaterial.getId(), 
                        videoMaterial.getPath()
                );

                if (success) {
                    // 转换成功，更新状态
                    videoMaterial.setIsHlsConverted(true);
                    videoMaterial.setIsHlsConverting(false);
                    videoMaterial.setHlsConvertTime(new Date());
                    log.info("HLS转换成功, videoId={}", videoMaterial.getId());
                } else {
                    // 转换失败，重置状态
                    videoMaterial.setIsHlsConverting(false);
                    videoMaterial.setHlsConvertRetryCount(
                            (videoMaterial.getHlsConvertRetryCount() == null ? 0 : videoMaterial.getHlsConvertRetryCount()) + 1
                    );
                    log.error("HLS转换失败, videoId={}, retryCount={}", 
                            videoMaterial.getId(), videoMaterial.getHlsConvertRetryCount());
                    
                    // 如果重试次数超过3次，标记为转换失败
                    if (videoMaterial.getHlsConvertRetryCount() >= 3) {
                        videoMaterial.setIsHlsConverted(false);
                        log.error("HLS转换重试次数超限，放弃转换, videoId={}", videoMaterial.getId());
                    }
                }

                videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);

            } catch (Exception e) {
                log.error("HLS转换过程中发生异常, videoId={}", videoMaterial.getId(), e);
                
                // 异常情况下重置转换状态
                videoMaterial.setIsHlsConverting(false);
                videoMaterialMapper.updateByPrimaryKeySelective(videoMaterial);
            }

        } catch (Exception e) {
            log.error("HLS转换定时任务执行异常", e);
        }
    }

    /**
     * 每小时清理一次转换中状态超时的记录
     * 防止因为异常导致的状态不一致
     */
    @Scheduled(fixedDelay = 3600000) // 1小时
    public void cleanupStuckConversions() {
        try {
            if (!scheduleEnabled) {
                return;
            }

            log.info("开始清理HLS转换超时记录");

            // 查询转换中状态超过1小时的记录
            VideoMaterialExample example = new VideoMaterialExample();
            example.createCriteria()
                    .andIsHlsConvertingEqualTo(true)
                    .andUpdateTimeLessThan(new Date(System.currentTimeMillis() - 3600000)); // 1小时前

            List<VideoMaterial> stuckMaterials = videoMaterialMapper.selectByExample(example);
            
            if (!CollectionUtils.isEmpty(stuckMaterials)) {
                for (VideoMaterial material : stuckMaterials) {
                    log.warn("发现HLS转换超时记录，重置状态, videoId={}", material.getId());
                    material.setIsHlsConverting(false);
                    videoMaterialMapper.updateByPrimaryKeySelective(material);
                }
                log.info("清理了{}个HLS转换超时记录", stuckMaterials.size());
            }

        } catch (Exception e) {
            log.error("清理HLS转换超时记录异常", e);
        }
    }
}
