# HLS视频播放401错误修复指南

## 🔍 问题分析

你遇到的401错误是由于HLS视频文件访问路径没有被正确配置导致的。主要问题包括：

1. **认证拦截**：HLS路径没有添加到认证忽略列表
2. **缺少Controller**：没有处理HLS文件访问的Controller
3. **CORS配置**：可能存在跨域访问问题

## ✅ 已修复的问题

### 1. 认证忽略列表更新

**修改文件**: `fastclip-backend-service/fastclip-starter/src/main/resources/application.yml`

```yaml
auth:
  ignoreList: login,videoPlay,upload,undefined,douyin/auth,downloadWithToken,ws/product-recognition,video/hls
```

**说明**: 添加了 `video/hls` 到忽略列表，这样HLS相关的请求就不会被认证拦截器拦截。

### 2. 创建HLS Controller

**新增文件**: `fastclip-backend-service/fastclip-biz/src/main/java/com/fastclip/biz/controller/HlsController.java`

**主要功能**:
- 处理 `.m3u8` 播放列表文件请求
- 处理 `.ts` 视频片段文件请求
- 提供CORS支持
- 安全检查（防止目录遍历攻击）
- 健康检查和调试接口

**关键接口**:
```java
// 播放列表文件
GET /api/fastclip/video/hls/{videoId}/playlist.m3u8

// 视频片段文件
GET /api/fastclip/video/hls/{videoId}/{filename}

// 健康检查
GET /api/fastclip/video/hls/health

// 调试接口（列出可用视频）
GET /api/fastclip/video/hls/list
```

## 🧪 测试步骤

### 1. 验证后端配置

#### 1.1 检查HLS健康状态
```bash
curl http://localhost:8078/api/fastclip/video/hls/health
```

**预期结果**: 返回HLS服务状态和基础路径信息

#### 1.2 列出可用视频
```bash
curl http://localhost:8078/api/fastclip/video/hls/list
```

**预期结果**: 显示HLS基础路径下的所有视频目录和文件信息

### 2. 验证HLS文件访问

#### 2.1 测试播放列表访问
```bash
# 替换123为实际的视频ID
curl -v http://localhost:8078/api/fastclip/video/hls/123/playlist.m3u8
```

**预期结果**: 
- HTTP状态码: 200
- Content-Type: application/vnd.apple.mpegurl
- 返回m3u8文件内容

#### 2.2 测试视频片段访问
```bash
# 替换123为实际的视频ID，segment_000.ts为实际的片段文件名
curl -v http://localhost:8078/api/fastclip/video/hls/123/segment_000.ts
```

**预期结果**:
- HTTP状态码: 200
- Content-Type: video/mp2t
- 返回二进制视频数据

### 3. 验证前端播放器

#### 3.1 检查浏览器网络请求
1. 打开浏览器开发者工具
2. 切换到Network标签
3. 打开商品识别界面
4. 观察HLS相关请求

**预期结果**:
- playlist.m3u8 请求返回200状态码
- .ts文件请求返回200状态码
- 没有401或403错误

#### 3.2 检查视频播放器状态
1. 观察视频播放器是否正常初始化
2. 检查是否显示错误信息
3. 尝试播放视频

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 404 Not Found
**可能原因**:
- HLS文件不存在
- 路径配置错误
- 文件权限问题

**解决方案**:
```bash
# 检查文件是否存在
ls -la /data/app/douyin/hls/{videoId}/

# 检查权限
sudo chown -R fastclip:fastclip /data/app/douyin/hls/
sudo chmod -R 755 /data/app/douyin/hls/

# 检查配置
curl http://localhost:8078/api/fastclip/video/hls/list
```

#### 2. 403 Forbidden
**可能原因**:
- 文件权限不足
- SELinux或AppArmor限制

**解决方案**:
```bash
# 修复权限
sudo chmod -R 755 /data/app/douyin/hls/

# 检查SELinux（如果适用）
sestatus
sudo setsebool -P httpd_can_network_connect 1
```

#### 3. CORS错误
**可能原因**:
- 跨域请求被阻止

**解决方案**:
HLS Controller已经包含了CORS头设置，如果仍有问题，检查前端代理配置。

#### 4. 视频播放器初始化失败
**可能原因**:
- Video.js依赖未正确加载
- HLS格式不兼容

**解决方案**:
```bash
# 检查HLS文件格式
ffprobe /data/app/douyin/hls/{videoId}/playlist.m3u8

# 重新转换HLS文件
./convert_to_hls.sh {videoId} /path/to/source/video.mp4
```

## 📋 验证清单

### 后端验证
- [ ] 认证忽略列表包含 `video/hls`
- [ ] HLS Controller正确部署
- [ ] HLS健康检查返回正常
- [ ] 可以列出可用视频
- [ ] playlist.m3u8文件可访问
- [ ] .ts片段文件可访问

### 前端验证
- [ ] 视频播放器正常初始化
- [ ] HLS URL生成正确
- [ ] 网络请求无401错误
- [ ] 视频可以正常播放
- [ ] 片段标记正常显示

### 文件系统验证
- [ ] HLS基础目录存在
- [ ] 视频目录结构正确
- [ ] 文件权限设置正确
- [ ] playlist.m3u8文件存在
- [ ] .ts片段文件存在

## 🚀 部署后验证

### 1. 重启服务
```bash
# 重启后端服务以加载新的Controller
sudo systemctl restart fastclip-backend

# 或者如果使用Docker
docker restart fastclip-backend
```

### 2. 验证配置生效
```bash
# 检查认证忽略是否生效
curl -v http://localhost:8078/api/fastclip/video/hls/health

# 应该返回200而不是401
```

### 3. 端到端测试
1. 打开前端应用
2. 选择一个视频进行商品标记
3. 观察视频播放器是否正常工作
4. 检查浏览器控制台是否有错误

## 📝 注意事项

### 安全考虑
- HLS Controller包含了目录遍历攻击防护
- 建议在生产环境中添加更严格的访问控制
- 考虑添加访问频率限制

### 性能优化
- .ts文件设置了缓存头（max-age=3600）
- .m3u8文件设置了no-cache以确保实时性
- 考虑使用CDN加速HLS文件分发

### 监控建议
- 监控HLS文件访问日志
- 设置磁盘空间告警
- 监控视频转换任务状态

## 🔗 相关文档

- [HLS视频转换指南](./HLS视频转换指南.md)
- [视频播放器功能测试指南](./视频播放器功能测试指南.md)
- [convert_to_hls.sh脚本](./convert_to_hls.sh)

## 📞 技术支持

如果问题仍然存在，请提供以下信息：
1. 浏览器控制台错误信息
2. 后端日志相关错误
3. HLS健康检查结果
4. 具体的视频ID和文件路径

这样可以更快速地定位和解决问题。
