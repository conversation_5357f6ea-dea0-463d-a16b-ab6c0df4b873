package com.fastclip.service.video;

import com.fastclip.common.constant.ItemTypeEnum;
import com.fastclip.common.model.dto.ItemDTO;
import com.fastclip.common.model.dto.SaveProductMarkingsResultDTO;
import com.fastclip.common.model.dto.VideoMaterialClipDTO;
import com.fastclip.common.model.dto.VideoMaterialDTO;
import com.fastclip.common.model.request.GetProductMarkingsReq;
import com.fastclip.common.model.request.SaveProductMarkingsReq;
import com.fastclip.common.model.request.VideoMaterialClipReq;
import com.fastclip.dao.mapper.ItemOnLiveMapper;
import com.fastclip.dao.mapper.VideoMaterialClipMapper;
import com.fastclip.dao.mapper.VideoMaterialMapper;
import com.fastclip.dao.model.dataobject.*;
import com.fastclip.dao.utils.VideoUtils;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class VideoMaterialClipService {
    @Autowired
    VideoMaterialClipMapper videoMaterialClipMapper;

    @Autowired
    ItemOnLiveMapper itemOnLiveMapper;

    @Autowired
    VideoMaterialService videoMaterialService;

    @Autowired
    LoadingCache<Long, ItemDTO> itemCache;

    public List<VideoMaterialClipDTO> getVideoMaterialClips(VideoMaterialClipReq req) {
        if(ItemTypeEnum.Import.getValue().equals(req.getItemType())) {
            VideoMaterialClipExample videoMaterialClipExample = new VideoMaterialClipExample();
            videoMaterialClipExample.createCriteria()
                    .andVideoIdEqualTo(req.getVideoId())
                    .andItemIdEqualTo(req.getItemId());
            List<VideoMaterialClip> videoMaterialClips = videoMaterialClipMapper.selectByExample(videoMaterialClipExample);
            videoMaterialClipExample.setOrderByClause("start_ts");
            return VideoUtils.do2DTOs(videoMaterialClips);
        }else{
            ItemDTO itemDTO = itemCache.get(req.getItemId());
            VideoMaterialDTO videoMaterialDTO = videoMaterialService.getVideoMaterialById(req.getVideoId());
            VideoMaterialClipDTO videoMaterialClipDTO = new VideoMaterialClipDTO();
            videoMaterialClipDTO.setVideoId(req.getVideoId());
            Date videoStartDate = videoMaterialDTO.getStartDate();
            videoMaterialClipDTO.setVideoDate(videoStartDate);
            Date itemCreateTime = itemDTO.getCreateTime();
            Integer startTs = (int)itemCreateTime.getTime() - (int)videoStartDate.getTime();
            videoMaterialClipDTO.setStartTs(startTs > 0 ? startTs : 0);
            videoMaterialClipDTO.setId(1L);
            videoMaterialClipDTO.setItemId(req.getItemId());
            List<VideoMaterialClipDTO> videoMaterialClipDTOS = new ArrayList<>();
            videoMaterialClipDTOS.add(videoMaterialClipDTO);
            return videoMaterialClipDTOS;
        }
    }

    /**
     * Save product markings to database
     * @param req Save product markings request
     * @return Save result
     */
    public SaveProductMarkingsResultDTO saveProductMarkings(SaveProductMarkingsReq req) {
        log.info("Saving product markings for videoId: {}, sellerId: {}, markings count: {}",
                req.getVideoId(), req.getSellerId(), req.getMarkings() != null ? req.getMarkings().size() : 0);

        // Validate request parameters
        if (req.getVideoId() == null) {
            log.error("VideoId is null in saveProductMarkings request");
            throw new IllegalArgumentException("视频ID不能为空");
        }
        if (req.getSellerId() == null) {
            log.error("SellerId is null in saveProductMarkings request");
            throw new IllegalArgumentException("达人ID不能为空");
        }
        if (req.getMarkings() == null || req.getMarkings().isEmpty()) {
            log.error("Markings list is null or empty in saveProductMarkings request");
            throw new IllegalArgumentException("标记列表不能为空");
        }

        int savedCount = 0;
        int updatedCount = 0;
        int skippedCount = 0;

        try {
            for (SaveProductMarkingsReq.ProductMarkingItem marking : req.getMarkings()) {
                // Validate marking data
                if (marking.getItemId() == null || marking.getStartTs() == null || marking.getEndTs() == null) {
                    log.warn("Skipping invalid marking: itemId={}, startTs={}, endTs={}",
                            marking.getItemId(), marking.getStartTs(), marking.getEndTs());
                    skippedCount++;
                    continue;
                }

                // Validate time range
                if (marking.getStartTs() >= marking.getEndTs()) {
                    log.warn("Skipping marking with invalid time range: startTs={}, endTs={}",
                            marking.getStartTs(), marking.getEndTs());
                    skippedCount++;
                    continue;
                }

                try {
                    // Check if marking already exists for this time segment
                    // We should check by (video_id, start_ts, end_ts) not by item_id
                    // because the same time segment might be re-identified with different items
                    VideoMaterialClipExample example = new VideoMaterialClipExample();
                    example.createCriteria()
                            .andVideoIdEqualTo(req.getVideoId())
                            .andStartTsEqualTo(marking.getStartTs())
                            .andEndTsEqualTo(marking.getEndTs());

                    List<VideoMaterialClip> existingClips = videoMaterialClipMapper.selectByExample(example);

                    VideoMaterialClip clip = new VideoMaterialClip();
                    clip.setVideoId(req.getVideoId());
                    clip.setSellerId(req.getSellerId());
                    clip.setItemId(marking.getItemId());
                    clip.setStartTs(marking.getStartTs());
                    clip.setEndTs(marking.getEndTs());
                    clip.setDuration(marking.getDuration());
                    clip.setUpdateTime(new Date());

                    if (existingClips.isEmpty()) {
                        // Insert new record
                        clip.setCreateTime(new Date());
                        int result = videoMaterialClipMapper.insertSelective(clip);
                        if (result > 0) {
                            savedCount++;
                            log.debug("Inserted new marking: videoId={}, itemId={}, startTs={}-{}",
                                    req.getVideoId(), marking.getItemId(), marking.getStartTs(), marking.getEndTs());
                        }
                    } else {
                        // Update existing record - replace with new item for same time segment
                        VideoMaterialClip existingClip = existingClips.get(0);
                        log.debug("Replacing existing marking: id={}, oldItemId={}, newItemId={}, timeSegment={}-{}",
                                existingClip.getId(), existingClip.getItemId(), marking.getItemId(),
                                marking.getStartTs(), marking.getEndTs());

                        clip.setId(existingClip.getId());
                        clip.setCreateTime(existingClip.getCreateTime());
                        int result = videoMaterialClipMapper.updateByPrimaryKeySelective(clip);
                        if (result > 0) {
                            updatedCount++;
                            log.debug("Successfully updated marking: id={}, videoId={}, newItemId={}, timeSegment={}-{}",
                                    clip.getId(), req.getVideoId(), marking.getItemId(), marking.getStartTs(), marking.getEndTs());
                        }
                    }
                } catch (Exception e) {
                    log.error("Error saving individual marking: videoId={}, itemId={}, startTs={}",
                            req.getVideoId(), marking.getItemId(), marking.getStartTs(), e);
                    skippedCount++;
                }
            }

            log.info("Product markings save completed: saved={}, updated={}, skipped={}, total={}",
                    savedCount, updatedCount, skippedCount, req.getMarkings().size());

            return new SaveProductMarkingsResultDTO(savedCount, updatedCount, savedCount + updatedCount);

        } catch (Exception e) {
            log.error("Error in saveProductMarkings for videoId: {}", req.getVideoId(), e);
            throw new RuntimeException("保存标记失败: " + e.getMessage(), e);
        }
    }

    /**
     * Get product markings by video ID
     * @param req Get product markings request
     * @return List of product markings with item names
     */
    public List<VideoMaterialClipDTO> getProductMarkings(GetProductMarkingsReq req) {
        log.info("Getting product markings for videoId: {}", req.getVideoId());

        if (req.getVideoId() == null) {
            log.error("VideoId is null in getProductMarkings request");
            throw new IllegalArgumentException("视频ID不能为空");
        }

        try {
            VideoMaterialClipExample example = new VideoMaterialClipExample();
            example.createCriteria().andVideoIdEqualTo(req.getVideoId());
            example.setOrderByClause("start_ts ASC");

            List<VideoMaterialClip> clips = videoMaterialClipMapper.selectByExample(example);
            List<VideoMaterialClipDTO> result = new ArrayList<>();

            log.debug("Found {} product markings for videoId: {}", clips.size(), req.getVideoId());

            for (VideoMaterialClip clip : clips) {
                try {
                    VideoMaterialClipDTO dto = VideoUtils.do2DTO(clip);

                    // Get item name from cache
                    try {
                        ItemDTO itemDTO = itemCache.get(clip.getItemId());
                        if (itemDTO != null) {
                            dto.setItemName(itemDTO.getItemName());
                            log.debug("Found item name for itemId {}: {}", clip.getItemId(), itemDTO.getItemName());
                        } else {
                            log.warn("Item not found in cache for itemId: {}", clip.getItemId());
                            dto.setItemName(null);
                        }
                    } catch (Exception e) {
                        log.warn("Error getting item from cache for itemId: {}", clip.getItemId(), e);
                        dto.setItemName(null);
                    }

                    result.add(dto);
                } catch (Exception e) {
                    log.error("Error processing clip with id: {}", clip.getId(), e);
                    // Continue processing other clips
                }
            }

            log.info("Successfully retrieved {} product markings for videoId: {}", result.size(), req.getVideoId());
            return result;

        } catch (Exception e) {
            log.error("Error in getProductMarkings for videoId: {}", req.getVideoId(), e);
            throw new RuntimeException("查询标记失败: " + e.getMessage(), e);
        }
    }
}
