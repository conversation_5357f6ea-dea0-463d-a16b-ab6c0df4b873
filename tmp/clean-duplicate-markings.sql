-- 清理video_material_clip表中的重复数据
-- 针对video_id=122的具体情况

-- 1. 查看当前重复数据的详细情况
SELECT 
    video_id, 
    start_ts, 
    end_ts, 
    GROUP_CONCAT(CONCAT('id:', id, ',item:', item_id, ',time:', update_time) ORDER BY update_time DESC) as records,
    COUNT(*) as count
FROM video_material_clip 
WHERE video_id = 122
GROUP BY video_id, start_ts, end_ts 
HAVING COUNT(*) > 1;

-- 2. 查看所有记录按时间段分组
SELECT 
    id,
    item_id,
    start_ts,
    end_ts,
    create_time,
    update_time,
    CASE 
        WHEN ROW_NUMBER() OVER (PARTITION BY video_id, start_ts, end_ts ORDER BY update_time DESC) = 1 
        THEN 'KEEP' 
        ELSE 'DELETE' 
    END as action
FROM video_material_clip 
WHERE video_id = 122
ORDER BY start_ts, update_time;

-- 3. 安全删除重复记录（保留最新的）
-- 首先创建一个临时表来标识要删除的记录
CREATE TEMPORARY TABLE temp_to_delete AS
SELECT id
FROM (
    SELECT 
        id,
        ROW_NUMBER() OVER (PARTITION BY video_id, start_ts, end_ts ORDER BY update_time DESC) as rn
    FROM video_material_clip 
    WHERE video_id = 122
) ranked
WHERE rn > 1;

-- 显示将要删除的记录
SELECT vmc.* 
FROM video_material_clip vmc
INNER JOIN temp_to_delete ttd ON vmc.id = ttd.id;

-- 执行删除（请先确认上面的查询结果正确）
-- DELETE vmc 
-- FROM video_material_clip vmc
-- INNER JOIN temp_to_delete ttd ON vmc.id = ttd.id;

-- 清理临时表
DROP TEMPORARY TABLE temp_to_delete;

-- 4. 验证清理结果
SELECT 
    video_id, 
    start_ts, 
    end_ts, 
    COUNT(*) as count
FROM video_material_clip 
WHERE video_id = 122
GROUP BY video_id, start_ts, end_ts 
HAVING COUNT(*) > 1;

-- 5. 查看最终的干净数据
SELECT 
    id,
    item_id,
    start_ts,
    end_ts,
    create_time,
    update_time
FROM video_material_clip 
WHERE video_id = 122
ORDER BY start_ts;
