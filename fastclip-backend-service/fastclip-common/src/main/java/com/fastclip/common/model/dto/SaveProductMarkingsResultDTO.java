package com.fastclip.common.model.dto;

import lombok.Data;

/**
 * Save product markings result DTO
 */
@Data
public class SaveProductMarkingsResultDTO {
    
    /**
     * Number of newly saved markings
     */
    private Integer savedCount;
    
    /**
     * Number of updated markings
     */
    private Integer updatedCount;
    
    /**
     * Total number of processed markings
     */
    private Integer totalCount;
    
    public SaveProductMarkingsResultDTO(Integer savedCount, Integer updatedCount, Integer totalCount) {
        this.savedCount = savedCount;
        this.updatedCount = updatedCount;
        this.totalCount = totalCount;
    }
}
