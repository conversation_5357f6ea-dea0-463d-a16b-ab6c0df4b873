package com.fastclip.biz.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * HLS Video Streaming Controller
 * Handles HTTP Live Streaming (HLS) video file requests
 */
@RestController
@RequestMapping("video/hls")
@Slf4j
public class HlsController {

    @Value("${video.hls.basePath}")
    private String hlsBasePath;

    /**
     * Serve HLS playlist file (.m3u8)
     * 
     * @param videoId Video ID
     * @return HLS playlist file
     */
    @GetMapping("/{videoId}/playlist.m3u8")
    public ResponseEntity<Resource> getPlaylist(@PathVariable Long videoId) {
        try {
            log.info("Requesting HLS playlist for video ID: {}", videoId);
            
            Path filePath = Paths.get(hlsBasePath, videoId.toString(), "playlist.m3u8");
            log.debug("Looking for playlist file at: {}", filePath.toAbsolutePath());
            
            if (!Files.exists(filePath)) {
                log.warn("HLS playlist not found: {}", filePath.toAbsolutePath());
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(filePath);
            
            if (resource.exists() && resource.isReadable()) {
                log.info("Serving HLS playlist for video ID: {}", videoId);
                return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, "application/vnd.apple.mpegurl")
                    .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                    .header(HttpHeaders.PRAGMA, "no-cache")
                    .header(HttpHeaders.EXPIRES, "0")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(resource);
            } else {
                log.error("HLS playlist exists but is not readable: {}", filePath.toAbsolutePath());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        } catch (Exception e) {
            log.error("Error serving HLS playlist for video ID: {}", videoId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Serve HLS segment files (.ts) and other related files
     * 
     * @param videoId Video ID
     * @param filename Segment filename (e.g., segment_000.ts)
     * @return HLS segment file
     */
    @GetMapping("/{videoId}/{filename:.+}")
    public ResponseEntity<Resource> getSegment(@PathVariable Long videoId, @PathVariable String filename) {
        try {
            log.debug("Requesting HLS segment for video ID: {}, filename: {}", videoId, filename);
            
            // Security check: prevent directory traversal
            if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
                log.warn("Invalid filename detected (potential directory traversal): {}", filename);
                return ResponseEntity.badRequest().build();
            }
            
            Path filePath = Paths.get(hlsBasePath, videoId.toString(), filename);
            log.debug("Looking for segment file at: {}", filePath.toAbsolutePath());
            
            if (!Files.exists(filePath)) {
                log.warn("HLS segment not found: {}", filePath.toAbsolutePath());
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(filePath);
            
            if (resource.exists() && resource.isReadable()) {
                // Determine content type based on file extension
                String contentType = getContentType(filename);
                String cacheControl = filename.endsWith(".ts") ? "max-age=3600" : "no-cache";
                
                log.debug("Serving HLS segment for video ID: {}, filename: {}, contentType: {}", 
                         videoId, filename, contentType);
                
                return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, contentType)
                    .header(HttpHeaders.CACHE_CONTROL, cacheControl)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(resource);
            } else {
                log.error("HLS segment exists but is not readable: {}", filePath.toAbsolutePath());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        } catch (Exception e) {
            log.error("Error serving HLS segment for video ID: {}, filename: {}", videoId, filename, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Handle CORS preflight requests
     */
    @RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptions() {
        return ResponseEntity.ok()
            .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
            .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, OPTIONS")
            .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
            .header(HttpHeaders.ACCESS_CONTROL_MAX_AGE, "3600")
            .build();
    }

    /**
     * Get appropriate content type for HLS files
     * 
     * @param filename File name
     * @return MIME content type
     */
    private String getContentType(String filename) {
        if (filename.endsWith(".m3u8")) {
            return "application/vnd.apple.mpegurl";
        } else if (filename.endsWith(".ts")) {
            return "video/mp2t";
        } else if (filename.endsWith(".key")) {
            return "application/octet-stream";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * Health check endpoint for HLS service
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        try {
            Path basePath = Paths.get(hlsBasePath);
            if (Files.exists(basePath) && Files.isDirectory(basePath)) {
                return ResponseEntity.ok("HLS service is healthy. Base path: " + basePath.toAbsolutePath());
            } else {
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body("HLS service unavailable. Base path not found: " + basePath.toAbsolutePath());
            }
        } catch (Exception e) {
            log.error("HLS health check failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("HLS service error: " + e.getMessage());
        }
    }

    /**
     * List available videos (for debugging purposes)
     */
    @GetMapping("/list")
    public ResponseEntity<String> listVideos() {
        try {
            Path basePath = Paths.get(hlsBasePath);
            if (!Files.exists(basePath)) {
                return ResponseEntity.ok("HLS base path does not exist: " + basePath.toAbsolutePath());
            }
            
            StringBuilder result = new StringBuilder();
            result.append("HLS Base Path: ").append(basePath.toAbsolutePath()).append("\n\n");
            result.append("Available Videos:\n");
            
            Files.list(basePath)
                .filter(Files::isDirectory)
                .forEach(dir -> {
                    String videoId = dir.getFileName().toString();
                    result.append("- Video ID: ").append(videoId).append("\n");
                    
                    try {
                        Path playlistPath = dir.resolve("playlist.m3u8");
                        if (Files.exists(playlistPath)) {
                            result.append("  ✓ playlist.m3u8 exists\n");
                            result.append("  URL: /api/fastclip/video/hls/").append(videoId).append("/playlist.m3u8\n");
                        } else {
                            result.append("  ✗ playlist.m3u8 missing\n");
                        }
                        
                        long tsCount = Files.list(dir)
                            .filter(file -> file.getFileName().toString().endsWith(".ts"))
                            .count();
                        result.append("  Segments: ").append(tsCount).append(" .ts files\n");
                        
                    } catch (Exception e) {
                        result.append("  Error reading directory: ").append(e.getMessage()).append("\n");
                    }
                    result.append("\n");
                });
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_TYPE, "text/plain; charset=utf-8")
                .body(result.toString());
                
        } catch (Exception e) {
            log.error("Error listing HLS videos", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error listing videos: " + e.getMessage());
        }
    }
}
