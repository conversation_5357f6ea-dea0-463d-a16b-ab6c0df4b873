/**
 * 测试0值startTs验证逻辑的修复
 */

// 模拟历史标记数据
const historicalMarkings = [
  {
    id: 9,
    itemId: 17,
    itemName: "棉质极简气质显瘦阔版外套女",
    startTs: 0,  // 第一个片段，startTs为0
    endTs: 10000,
    duration: 10000
  },
  {
    id: 10,
    itemId: 9,
    itemName: "亚麻天丝撞色条纹涂层风衣外套",
    startTs: 10000,
    endTs: 20000,
    duration: 10000
  }
];

console.log('=== 测试JavaScript中0值的验证逻辑 ===');

// 原始的错误验证逻辑
function validateMarkingOld(marking) {
  if (!marking.startTs || !marking.endTs || !marking.itemId) {
    return false;
  }
  return true;
}

// 修复后的验证逻辑
function validateMarkingNew(marking) {
  if (marking.startTs === null || marking.startTs === undefined || 
      marking.endTs === null || marking.endTs === undefined || 
      !marking.itemId) {
    return false;
  }
  return true;
}

console.log('\n--- 原始验证逻辑测试 ---');
historicalMarkings.forEach((marking, index) => {
  const isValid = validateMarkingOld(marking);
  console.log(`标记 ${index + 1} (startTs: ${marking.startTs}): ${isValid ? '✅ 有效' : '❌ 无效'}`);
  if (!isValid) {
    console.log(`  原因: !${marking.startTs} = ${!marking.startTs}`);
  }
});

console.log('\n--- 修复后验证逻辑测试 ---');
historicalMarkings.forEach((marking, index) => {
  const isValid = validateMarkingNew(marking);
  console.log(`标记 ${index + 1} (startTs: ${marking.startTs}): ${isValid ? '✅ 有效' : '❌ 无效'}`);
});

console.log('\n--- JavaScript中0值的真假性测试 ---');
console.log('!0 =', !0);
console.log('!10000 =', !10000);
console.log('0 === null =', 0 === null);
console.log('0 === undefined =', 0 === undefined);
console.log('typeof 0 =', typeof 0);

console.log('\n--- 推荐的验证方式 ---');
const testValues = [0, null, undefined, '', false, 10000];
testValues.forEach(value => {
  console.log(`值: ${value} (${typeof value})`);
  console.log(`  !value: ${!value}`);
  console.log(`  value === null: ${value === null}`);
  console.log(`  value === undefined: ${value === undefined}`);
  console.log(`  typeof value === 'number': ${typeof value === 'number'}`);
  console.log('---');
});

console.log('\n=== 结论 ===');
console.log('❌ 错误方式: !marking.startTs (会把0当作false)');
console.log('✅ 正确方式: marking.startTs === null || marking.startTs === undefined');
console.log('✅ 或者使用: typeof marking.startTs !== "number"');
console.log('✅ 或者使用: marking.startTs == null (同时检查null和undefined)');
