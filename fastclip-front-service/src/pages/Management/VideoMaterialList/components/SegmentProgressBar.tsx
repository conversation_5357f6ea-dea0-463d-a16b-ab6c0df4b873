import React from 'react';
import { Tooltip, Dropdown } from 'antd';
import { LoadingOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { colorManager, type SegmentProgressBarProps } from './types';
import styles from './ProductMarkingModal.less';

const SegmentProgressBar: React.FC<SegmentProgressBarProps> = ({
  duration,
  segments,
  currentTime = 0,
  onSegmentClick,
  onManualSelect,
}) => {
  const currentIdx = Math.min(
    segments.length - 1,
    Math.floor(currentTime / 10000),
  );

  const formatTime = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSegmentClick = (idx: number) => {
    onSegmentClick?.(idx);
  };

  // Generate context menu for manual selection
  const getContextMenu = (segment: any, segmentIndex: number) => {
    // Only show context menu for completed or saved segments
    if (segment.status !== 'completed' && segment.status !== 'saved') {
      return null;
    }

    // Collect unique products from current items and original items only
    const allProducts = new Map<string, { itemId: string; itemName: string; color: string }>();

    segments.forEach(seg => {
      if (seg.status === 'completed' || seg.status === 'saved') {
        // Add current displayed item (might be manually selected)
        if (seg.itemId) {
          const color = colorManager.getColorForItem(seg.itemId);
          allProducts.set(seg.itemId, {
            itemId: seg.itemId,
            itemName: seg.itemName || seg.itemId,
            color
          });
        }

        // Add original recognition result (if it was manually replaced)
        if (seg.originalItemId && seg.originalItemId !== seg.itemId) {
          const color = colorManager.getColorForItem(seg.originalItemId);
          allProducts.set(seg.originalItemId, {
            itemId: seg.originalItemId,
            itemName: seg.originalItemName || seg.originalItemId,
            color
          });
        }
      }
    });

    // If no products found in the entire video, don't show menu
    if (allProducts.size === 0) {
      return null;
    }

    // Create menu items for all products, sorted by itemId for consistency
    const sortedProducts = Array.from(allProducts.values()).sort((a, b) =>
      a.itemId.localeCompare(b.itemId)
    );

    const menuItems = sortedProducts.map((product) => {
      // Check if this product is the current segment's item or original item
      let currentSegmentConfidence: number | undefined;

      if (segment.itemId === product.itemId) {
        // This is the current displayed item
        currentSegmentConfidence = segment.confidence;
      } else if (segment.originalItemId === product.itemId) {
        // This is the original recognition result
        currentSegmentConfidence = segment.originalConfidence;
      }

      return {
        key: `${segmentIndex}-${product.itemId}`,
        label: (
          <div className={styles['product-menu-item']} style={{
            display: 'flex',
            alignItems: 'center',
            gap: 10,
            padding: '6px 4px',
            minWidth: 220
          }}>
            {/* Color indicator */}
            <div
              className={styles['color-indicator']}
              style={{
                width: 14,
                height: 14,
                borderRadius: '50%',
                backgroundColor: product.color,
                border: '2px solid rgba(255,255,255,0.8)',
                flexShrink: 0,
                boxShadow: `0 0 0 1px ${product.color}40`
              }}
            />

            {/* Product info */}
            <div className={styles['product-info']} style={{ flex: 1, minWidth: 0 }}>
              <div className={styles['product-id']} style={{
                fontWeight: 600,
                fontSize: '13px',
                color: '#1a1a1a',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {product.itemId}
              </div>
              <div className={styles['product-name']} style={{
                fontSize: '11px',
                color: '#666',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {product.itemName}
              </div>
            </div>

            {/* Confidence badge (only if exists in current segment) */}
            {currentSegmentConfidence !== undefined && (
              <div className={styles['confidence-badge']} style={{
                fontSize: '10px',
                color: '#555',
                fontWeight: 600,
                flexShrink: 0,
                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                border: '1px solid #dee2e6',
                padding: '2px 6px',
                borderRadius: '8px'
              }}>
                {(currentSegmentConfidence * 100).toFixed(1)}%
              </div>
            )}
          </div>
        ),
        onClick: () => {
          // Use the confidence from current segment if available, otherwise use a default
          const selectedItem = {
            itemId: product.itemId,
            itemName: product.itemName,
            confidence: currentSegmentConfidence || 0.5
          };
          onManualSelect?.(segmentIndex, selectedItem);
        }
      };
    });

    // Add a header item to explain the menu
    const headerItem = {
      key: 'header',
      type: 'group' as const,
      label: (
        <div style={{
          fontSize: '12px',
          color: '#666',
          fontWeight: 500,
          padding: '4px 0',
          borderBottom: '1px solid #f0f0f0',
          marginBottom: '4px'
        }}>
          选择商品 ({allProducts.size}个可选)
        </div>
      )
    };

    return {
      items: [headerItem, ...menuItems]
    };
  };



  const getSegmentContent = (segment: any, idx: number) => {
    const timeRange = `${formatTime(segment.start)}-${formatTime(segment.end)}`;

    if (segment.status === 'processing') {
      return (
        <>
          <div className={styles.segmentTime}>{timeRange}</div>
          <div className={styles.segmentLabel}>
            <LoadingOutlined spin /> 识别中
          </div>
        </>
      );
    }

    if (segment.status === 'error') {
      return (
        <>
          <div className={styles.segmentTime}>{timeRange}</div>
          <div className={styles.segmentLabel}>
            <ExclamationCircleOutlined /> 错误
          </div>
        </>
      );
    }

    if (segment.status === 'completed' || segment.status === 'saved') {
      if (segment.itemId) {
        // Successfully recognized item
        return (
          <>
            <div className={styles.segmentTime}>{timeRange}</div>
            <div className={styles.segmentLabel}>
              <CheckCircleOutlined /> {segment.itemId}
            </div>
            <div className={styles.confidenceScore}>
              {segment.status === 'saved' ? '已保存' :
               segment.isManuallySelected ? '手动选择' : `${(segment.confidence * 100).toFixed(0)}%`}
            </div>
          </>
        );
      } else {
        // Recognition failed
        return (
          <>
            <div className={styles.segmentTime}>{timeRange}</div>
            <div className={styles.segmentLabel}>
              <ExclamationCircleOutlined /> 识别失败
            </div>
          </>
        );
      }
    }

    return (
      <>
        <div className={styles.segmentTime}>{timeRange}</div>
        <div className={styles.segmentLabel}>待处理</div>
      </>
    );
  };

  return (
    <div className={styles['segment-progress-bar']}>
      <div className={styles.wrapper}>
        {segments.map((seg, idx) => {
          const percent = (100 / segments.length) + '%';
          const isActive = idx === currentIdx;

          // Get dynamic color for this segment based on itemId
          const segmentColor = (seg.status === 'completed' || seg.status === 'saved') ?
            colorManager.getColorForItem(seg.itemId) :
            undefined;

          let segmentClass = styles.segment;
          if (isActive) segmentClass += ` ${styles.active}`;
          if (seg.status) segmentClass += ` ${styles[seg.status]}`;

          // Build comprehensive tooltip content with proper line breaks
          const tooltipContent = (
            <div>
              <div>时间段: {formatTime(seg.start)} - {formatTime(seg.end)}</div>
              {(seg.status === 'completed' || seg.status === 'saved') && seg.itemId && (
                <>
                  {seg.status === 'saved' && <div>状态: 已保存到数据库</div>}
                  {seg.isManuallySelected ? (
                    <>
                      <div>已手动选择: {seg.itemName || seg.itemId}</div>
                      {seg.originalItemId && (
                        <>
                          <div>识别结果: {seg.originalItemName || seg.originalItemId}</div>
                          {seg.originalConfidence && <div>识别置信度: {(seg.originalConfidence * 100).toFixed(1)}%</div>}
                        </>
                      )}
                    </>
                  ) : (
                    <>
                      <div>商品ID: {seg.itemId}</div>
                      {seg.itemName && <div>商品名称: {seg.itemName}</div>}
                      {seg.confidence && <div>置信度: {(seg.confidence * 100).toFixed(1)}%</div>}
                    </>
                  )}
                </>
              )}
              {(seg.status === 'completed' || seg.status === 'saved') && !seg.itemId && <div>状态: 识别失败</div>}
              {seg.status === 'processing' && <div>状态: 识别中...</div>}
              {seg.status === 'error' && <div>状态: 识别错误</div>}
              {seg.status === 'pending' && <div>状态: 待处理</div>}
            </div>
          );

          const segmentStyle: React.CSSProperties = {
            width: percent,
            ...(segmentColor && (seg.status === 'completed' || seg.status === 'saved') ? { backgroundColor: segmentColor } : {})
          };

          const contextMenu = getContextMenu(seg, idx);

          const segmentElement = (
            <Tooltip title={tooltipContent} placement="top">
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '2px'
                }}
                onClick={() => handleSegmentClick(idx)}
              >
                {getSegmentContent(seg, idx)}
              </div>
            </Tooltip>
          );

          return contextMenu ? (
            <Dropdown key={idx} menu={contextMenu} trigger={['contextMenu']}>
              <div
                className={segmentClass}
                style={segmentStyle}
              >
                {segmentElement}
              </div>
            </Dropdown>
          ) : (
            <Tooltip key={idx} title={tooltipContent} placement="top">
              <div
                className={segmentClass}
                style={segmentStyle}
                onClick={() => handleSegmentClick(idx)}
              >
                {getSegmentContent(seg, idx)}
              </div>
            </Tooltip>
          );
        })}
      </div>
    </div>
  );
};

export default SegmentProgressBar;
