/**
 * 商品标记API测试脚本
 * 使用Node.js运行: node test-product-markings-api.js
 */

const http = require('http');

// 配置
const config = {
  host: 'localhost',
  port: 8078,
  timeout: 30000
};

// 测试数据
const testData = {
  videoId: 123,
  sellerId: 456,
  markings: [
    {
      itemId: 789,
      startTs: 10000,
      endTs: 20000,
      duration: 10000
    },
    {
      itemId: 790,
      startTs: 30000,
      endTs: 40000,
      duration: 10000
    }
  ]
};

// HTTP请求工具函数
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: response
          });
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(config.timeout, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 测试保存标记API
async function testSaveProductMarkings() {
  console.log('\n=== 测试保存商品标记API ===');
  
  const options = {
    hostname: config.host,
    port: config.port,
    path: '/api/fastclip/video/saveProductMarkings',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(JSON.stringify(testData))
    }
  };
  
  try {
    console.log('发送请求:', JSON.stringify(testData, null, 2));
    const response = await makeRequest(options, testData);
    
    console.log('响应状态码:', response.statusCode);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data.resultCode === 0) {
      console.log('✅ 保存成功!');
      return true;
    } else {
      console.log('❌ 保存失败:', response.data.resultDesc);
      return false;
    }
  } catch (error) {
    console.log('❌ 请求失败:', error.message);
    return false;
  }
}

// 测试查询标记API
async function testGetProductMarkings() {
  console.log('\n=== 测试查询商品标记API ===');
  
  const queryData = {
    videoId: testData.videoId
  };
  
  const options = {
    hostname: config.host,
    port: config.port,
    path: '/api/fastclip/video/getProductMarkings',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(JSON.stringify(queryData))
    }
  };
  
  try {
    console.log('发送请求:', JSON.stringify(queryData, null, 2));
    const response = await makeRequest(options, queryData);
    
    console.log('响应状态码:', response.statusCode);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data.resultCode === 0) {
      console.log('✅ 查询成功!');
      console.log(`找到 ${response.data.result.length} 条标记记录`);
      return true;
    } else {
      console.log('❌ 查询失败:', response.data.resultDesc);
      return false;
    }
  } catch (error) {
    console.log('❌ 请求失败:', error.message);
    return false;
  }
}

// 测试错误处理
async function testErrorHandling() {
  console.log('\n=== 测试错误处理 ===');
  
  // 测试无效参数
  const invalidData = {
    videoId: null,
    sellerId: null,
    markings: []
  };
  
  const options = {
    hostname: config.host,
    port: config.port,
    path: '/api/fastclip/video/saveProductMarkings',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(JSON.stringify(invalidData))
    }
  };
  
  try {
    console.log('发送无效请求:', JSON.stringify(invalidData, null, 2));
    const response = await makeRequest(options, invalidData);
    
    console.log('响应状态码:', response.statusCode);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data.resultCode !== 0) {
      console.log('✅ 错误处理正常，正确拒绝了无效请求');
      return true;
    } else {
      console.log('❌ 错误处理异常，应该拒绝无效请求');
      return false;
    }
  } catch (error) {
    console.log('❌ 请求失败:', error.message);
    return false;
  }
}

// 性能测试
async function testPerformance() {
  console.log('\n=== 性能测试 ===');
  
  // 创建大量标记数据
  const largeTestData = {
    videoId: 999,
    sellerId: 456,
    markings: []
  };
  
  // 生成100个标记片段
  for (let i = 0; i < 100; i++) {
    largeTestData.markings.push({
      itemId: 1000 + i,
      startTs: i * 10000,
      endTs: (i + 1) * 10000,
      duration: 10000
    });
  }
  
  const options = {
    hostname: config.host,
    port: config.port,
    path: '/api/fastclip/video/saveProductMarkings',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(JSON.stringify(largeTestData))
    }
  };
  
  try {
    console.log(`发送大量数据请求 (${largeTestData.markings.length} 条标记)`);
    const startTime = Date.now();
    const response = await makeRequest(options, largeTestData);
    const endTime = Date.now();
    
    console.log('响应状态码:', response.statusCode);
    console.log('处理时间:', endTime - startTime, 'ms');
    
    if (response.data.resultCode === 0) {
      console.log('✅ 性能测试通过!');
      console.log('处理结果:', response.data.result);
      return true;
    } else {
      console.log('❌ 性能测试失败:', response.data.resultDesc);
      return false;
    }
  } catch (error) {
    console.log('❌ 性能测试失败:', error.message);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始API测试...');
  console.log(`目标服务器: http://${config.host}:${config.port}`);
  
  const results = [];
  
  // 运行所有测试
  results.push(await testSaveProductMarkings());
  results.push(await testGetProductMarkings());
  results.push(await testErrorHandling());
  results.push(await testPerformance());
  
  // 统计结果
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log('\n=== 测试结果汇总 ===');
  console.log(`通过: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('🎉 所有测试通过!');
  } else {
    console.log('⚠️  部分测试失败，请检查服务器状态和配置');
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testSaveProductMarkings,
  testGetProductMarkings,
  testErrorHandling,
  testPerformance
};
