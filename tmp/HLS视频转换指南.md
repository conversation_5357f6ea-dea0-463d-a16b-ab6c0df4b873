# HLS视频转换指南

## 📋 概述

本指南详细说明如何将现有视频转换为HLS格式，以及路径和文件名的确定规则。

## 🗂️ 路径和文件名规则

### 配置文件路径
根据 `application.yml` 配置：

```yaml
ffmpeg:
  hlsTmpPath: ${BASE_DATA_PATH:/data/app}/douyin/tmp/hls

video:
  hls:
    basePath: ${BASE_DATA_PATH:/data/app}/douyin/hls/
    urlPrefix: ${HLS_URL_PREFIX:http://localhost:8078/api/fastclip/video/hls/}
    segmentDuration: 4  # seconds per segment
    playlistType: vod   # video on demand
```

### 文件路径规则

#### 1. HLS文件存储路径
```
基础路径: /data/app/douyin/hls/
完整路径: /data/app/douyin/hls/{videoId}/
```

#### 2. 文件命名规则
```
主播放列表: playlist.m3u8
视频片段: segment_000.ts, segment_001.ts, segment_002.ts, ...
```

#### 3. URL访问路径
```
前端访问URL: /api/fastclip/video/hls/{videoId}/playlist.m3u8
完整URL示例: http://localhost:8078/api/fastclip/video/hls/123/playlist.m3u8
```

### 根据代码分析的路径生成逻辑

从 `LiveVideoService.java` 中可以看到路径生成规则：
```java
public String getVideoMaterialSliceBasePath(VideoMaterialDTO videoMaterialDTO) {
    DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
    String dateTimeStr = dateFormat.format(videoMaterialDTO.getStartDate());
    return hlsTmpPath + "/" + videoMaterialDTO.getSellerId() + "_" + dateTimeStr + videoMaterialDTO.getStartTime();
}
```

## 🔧 FFmpeg转换命令

### 基础HLS转换命令
```bash
ffmpeg -i input.mp4 \
       -preset veryfast \
       -g 48 \
       -sc_threshold 0 \
       -c:v libx264 \
       -b:v 4000k \
       -profile:v main \
       -c:a aac \
       -b:a 128k \
       -hls_time 4 \
       -hls_playlist_type vod \
       -hls_segment_filename "/data/app/douyin/hls/{videoId}/segment_%03d.ts" \
       "/data/app/douyin/hls/{videoId}/playlist.m3u8"
```

### 参数说明
- `-hls_time 4`: 每个片段4秒（与配置一致）
- `-hls_playlist_type vod`: 点播模式
- `-hls_segment_filename`: 片段文件命名模板
- `-preset veryfast`: 快速编码预设
- `-g 48`: GOP大小，影响seek精度

### GPU加速版本（如果支持）
```bash
ffmpeg -i input.mp4 \
       -c:v h264_nvenc \
       -preset fast \
       -b:v 4000k \
       -c:a aac \
       -b:a 128k \
       -hls_time 4 \
       -hls_playlist_type vod \
       -hls_segment_filename "/data/app/douyin/hls/{videoId}/segment_%03d.ts" \
       "/data/app/douyin/hls/{videoId}/playlist.m3u8"
```

## 🚀 实现方案

### 方案1: 手动转换脚本

创建转换脚本 `convert_to_hls.sh`:

```bash
#!/bin/bash

# 参数检查
if [ $# -ne 2 ]; then
    echo "用法: $0 <视频ID> <输入视频路径>"
    echo "示例: $0 123 /data/app/seller/video.mp4"
    exit 1
fi

VIDEO_ID=$1
INPUT_PATH=$2
BASE_PATH="/data/app/douyin/hls"
OUTPUT_DIR="${BASE_PATH}/${VIDEO_ID}"
OUTPUT_PLAYLIST="${OUTPUT_DIR}/playlist.m3u8"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 检查输入文件
if [ ! -f "$INPUT_PATH" ]; then
    echo "错误: 输入文件不存在: $INPUT_PATH"
    exit 1
fi

echo "开始转换视频 ID: $VIDEO_ID"
echo "输入文件: $INPUT_PATH"
echo "输出目录: $OUTPUT_DIR"

# 执行FFmpeg转换
ffmpeg -i "$INPUT_PATH" \
       -preset veryfast \
       -g 48 \
       -sc_threshold 0 \
       -c:v libx264 \
       -b:v 4000k \
       -profile:v main \
       -c:a aac \
       -b:a 128k \
       -hls_time 4 \
       -hls_playlist_type vod \
       -hls_segment_filename "${OUTPUT_DIR}/segment_%03d.ts" \
       "$OUTPUT_PLAYLIST" \
       -y

if [ $? -eq 0 ]; then
    echo "转换成功!"
    echo "播放列表: $OUTPUT_PLAYLIST"
    echo "访问URL: http://localhost:8078/api/fastclip/video/hls/${VIDEO_ID}/playlist.m3u8"
else
    echo "转换失败!"
    exit 1
fi
```

### 方案2: Java后端集成

创建HLS转换服务类:

```java
@Service
@Slf4j
public class HlsConversionService {
    
    @Value("${video.hls.basePath}")
    private String hlsBasePath;
    
    @Value("${video.hls.segmentDuration}")
    private int segmentDuration;
    
    @Value("${ffmpeg.processor}")
    private String processor;
    
    public boolean convertToHls(Long videoId, String inputPath) {
        try {
            String outputDir = hlsBasePath + "/" + videoId;
            String outputPlaylist = outputDir + "/playlist.m3u8";
            
            // 创建输出目录
            Files.createDirectories(Paths.get(outputDir));
            
            // 构建FFmpeg命令
            List<String> command = buildFfmpegCommand(inputPath, outputDir, outputPlaylist);
            
            // 执行转换
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();
            
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                log.info("HLS conversion successful for video ID: {}", videoId);
                return true;
            } else {
                log.error("HLS conversion failed for video ID: {}, exit code: {}", videoId, exitCode);
                return false;
            }
            
        } catch (Exception e) {
            log.error("Error converting video to HLS: {}", e.getMessage(), e);
            return false;
        }
    }
    
    private List<String> buildFfmpegCommand(String inputPath, String outputDir, String outputPlaylist) {
        List<String> command = new ArrayList<>();
        command.add("ffmpeg");
        command.add("-i");
        command.add(inputPath);
        
        // 视频编码设置
        if ("gpu".equals(processor)) {
            command.add("-c:v");
            command.add("h264_nvenc");
            command.add("-preset");
            command.add("fast");
        } else {
            command.add("-c:v");
            command.add("libx264");
            command.add("-preset");
            command.add("veryfast");
        }
        
        command.add("-g");
        command.add("48");
        command.add("-sc_threshold");
        command.add("0");
        command.add("-b:v");
        command.add("4000k");
        command.add("-profile:v");
        command.add("main");
        
        // 音频编码设置
        command.add("-c:a");
        command.add("aac");
        command.add("-b:a");
        command.add("128k");
        
        // HLS设置
        command.add("-hls_time");
        command.add(String.valueOf(segmentDuration));
        command.add("-hls_playlist_type");
        command.add("vod");
        command.add("-hls_segment_filename");
        command.add(outputDir + "/segment_%03d.ts");
        
        command.add(outputPlaylist);
        command.add("-y");
        
        return command;
    }
}
```

## 📁 目录结构示例

转换后的目录结构：
```
/data/app/douyin/hls/
├── 123/                    # 视频ID为123
│   ├── playlist.m3u8       # 主播放列表
│   ├── segment_000.ts      # 第1个片段 (0-4秒)
│   ├── segment_001.ts      # 第2个片段 (4-8秒)
│   ├── segment_002.ts      # 第3个片段 (8-12秒)
│   └── ...
├── 456/                    # 视频ID为456
│   ├── playlist.m3u8
│   ├── segment_000.ts
│   └── ...
└── ...
```

## 🔗 后端API接口

需要添加静态资源访问接口：

```java
@RestController
@RequestMapping("/video/hls")
public class HlsController {
    
    @Value("${video.hls.basePath}")
    private String hlsBasePath;
    
    @GetMapping("/{videoId}/playlist.m3u8")
    public ResponseEntity<Resource> getPlaylist(@PathVariable Long videoId) {
        try {
            Path filePath = Paths.get(hlsBasePath, videoId.toString(), "playlist.m3u8");
            Resource resource = new FileSystemResource(filePath);
            
            if (resource.exists()) {
                return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, "application/vnd.apple.mpegurl")
                    .header(HttpHeaders.CACHE_CONTROL, "no-cache")
                    .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/{videoId}/{filename:.+}")
    public ResponseEntity<Resource> getSegment(@PathVariable Long videoId, @PathVariable String filename) {
        try {
            Path filePath = Paths.get(hlsBasePath, videoId.toString(), filename);
            Resource resource = new FileSystemResource(filePath);
            
            if (resource.exists()) {
                String contentType = filename.endsWith(".ts") ? "video/mp2t" : "application/octet-stream";
                return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, contentType)
                    .header(HttpHeaders.CACHE_CONTROL, "max-age=3600")
                    .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
```

## ⚡ 快速开始

### 1. 准备环境
```bash
# 确保FFmpeg已安装
ffmpeg -version

# 创建HLS目录
sudo mkdir -p /data/app/douyin/hls
sudo chown -R $USER:$USER /data/app/douyin/hls
```

### 2. 转换示例视频
```bash
# 假设有视频ID为123的视频文件
./convert_to_hls.sh 123 /path/to/your/video.mp4
```

### 3. 验证转换结果
```bash
# 检查生成的文件
ls -la /data/app/douyin/hls/123/

# 测试播放列表
curl http://localhost:8078/api/fastclip/video/hls/123/playlist.m3u8
```

## 🔍 故障排除

### 常见问题

1. **权限问题**
   ```bash
   sudo chown -R fastclip:fastclip /data/app/douyin/hls
   sudo chmod -R 755 /data/app/douyin/hls
   ```

2. **FFmpeg未找到**
   ```bash
   which ffmpeg
   # 如果没有安装，使用包管理器安装
   sudo apt install ffmpeg  # Ubuntu/Debian
   sudo yum install ffmpeg  # CentOS/RHEL
   ```

3. **磁盘空间不足**
   ```bash
   df -h /data/app/douyin/hls
   # 清理旧的HLS文件或扩展磁盘空间
   ```

4. **网络访问问题**
   - 检查防火墙设置
   - 确认端口8078开放
   - 验证nginx代理配置（如果使用）

## 📝 注意事项

1. **存储空间**: HLS文件通常比原视频大20-30%
2. **转换时间**: 取决于视频长度和服务器性能
3. **并发转换**: 避免同时转换过多视频，会影响系统性能
4. **清理策略**: 定期清理不需要的HLS文件
5. **备份**: 重要视频建议保留原文件

这个指南提供了完整的HLS转换方案，你可以根据实际需求选择手动脚本或Java集成的方式。
